# 智能体发言意愿功能测试指南

## 功能概述

新增的智能体发言意愿判断功能让智能体在自由讨论模式下能够自主决定是否发言，避免重复观点和无关发言。

## 测试场景

### 1. 基础发言意愿测试

**测试步骤：**
1. 创建3-4个不同专业领域的智能体
2. 配置不同的性格特征（assertive、collaborative、diplomatic等）
3. 启动自由讨论模式
4. 观察智能体的发言模式

**预期结果：**
- 专业相关的智能体更倾向于发言
- 不同性格的智能体表现出不同的发言倾向
- 控制台显示发言意愿查询的日志

### 2. 观点重复避免测试

**测试步骤：**
1. 选择一个较窄的讨论话题
2. 让讨论进行一段时间，直到观点开始重复
3. 观察智能体是否开始拒绝发言

**预期结果：**
- 当观点已被充分讨论时，智能体选择不发言
- 不发言的理由应该提到"观点重复"或"已被讨论"

### 3. 主持人介入测试

**测试步骤：**
1. 创建一个主持人智能体
2. 设置一个话题，让所有参与者都不太愿意发言
3. 观察主持人的处理方式

**预期结果：**
- 当所有智能体都不愿发言时，主持人会介入
- 主持人会分析讨论状态，决定结束或提出新问题
- 如果提出新问题，讨论会继续

### 4. 主持人模式不受影响测试

**测试步骤：**
1. 使用主持人模式启动讨论
2. 观察发言顺序是否仍然按轮流进行
3. 确认主持人可以指定任何智能体发言

**预期结果：**
- 主持人模式的行为完全不变
- 智能体按照主持人安排发言，不受发言意愿限制

### 5. 性格特征影响测试

**测试步骤：**
1. 创建相同专业但不同性格的智能体
2. 在不同讨论阶段观察它们的发言倾向

**预期结果：**
- assertive（果断型）：更主动发言
- collaborative（协作型）：在需要协调时发言
- diplomatic（外交型）：在有分歧时发言
- thoughtful（深思型）：在深度思考后发言

## 测试数据准备

### 智能体配置示例

```json
{
  "name": "技术专家Alice",
  "expertise": ["软件开发", "系统架构"],
  "thinkingStyle": "analytical",
  "personality": "assertive"
}

{
  "name": "产品经理Bob",
  "expertise": ["产品管理", "用户体验"],
  "thinkingStyle": "systematic",
  "personality": "collaborative"
}

{
  "name": "设计师Carol",
  "expertise": ["UI设计", "用户体验"],
  "thinkingStyle": "creative",
  "personality": "thoughtful"
}
```

### 测试话题

1. **技术相关**："如何提高软件开发效率"
2. **产品相关**："新产品功能优先级排序"
3. **跨领域**："团队协作最佳实践"
4. **窄话题**："按钮颜色选择"

## 观察要点

### 控制台日志
- 发言意愿查询的结果
- 主持人介入的触发条件
- 讨论结束的原因

### 讨论质量
- 是否减少了重复观点
- 讨论是否更加聚焦
- 智能体发言是否更有针对性

### 用户体验
- 讨论流程是否自然
- 主持人介入是否合理
- 结束时机是否恰当

## 常见问题排查

### 1. 所有智能体都不发言
- 检查LLM配置是否正确
- 确认话题是否过于狭窄
- 查看控制台错误信息

### 2. 发言意愿判断不准确
- 检查智能体的专业领域配置
- 确认性格特征设置
- 查看LLM响应的原始内容

### 3. 主持人介入异常
- 确认主持人配置了LLM
- 检查主持人的系统提示词
- 查看主持人决策的日志

## 性能监控

- 发言意愿查询的响应时间
- LLM API调用频率
- 内存使用情况
- 讨论长度和质量的平衡

## 优化建议

基于测试结果，可以考虑以下优化：

1. **缓存机制**：对相似讨论状态的发言意愿结果进行缓存
2. **批量查询**：同时查询多个智能体的发言意愿
3. **阈值调整**：根据实际效果调整各种判断阈值
4. **提示词优化**：根据LLM响应质量优化提示词模板
