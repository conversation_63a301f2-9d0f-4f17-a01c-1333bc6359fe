# 智能体发言意愿功能使用指南

## 功能介绍

智能体发言意愿功能让AI智能体能够自主判断是否需要在讨论中发言，避免重复观点和无关发言，提高讨论质量。

## 主要特性

### 1. 智能发言判断
- 智能体根据专业领域相关性判断是否发言
- 避免发表已被认可的重复观点
- 考虑性格特征对发言时机的影响

### 2. 主持人智能介入
- 当所有智能体都不愿发言时，主持人自动介入
- 分析讨论完成度，决定结束或继续
- 提出新问题激发进一步讨论

### 3. 模式区分
- **自由讨论模式**：启用发言意愿判断
- **主持人模式**：保持原有轮流发言机制

## 使用方法

### 1. 智能体配置

为了获得最佳效果，请合理配置智能体：

**专业领域设置**
- 设置明确的专业领域，如"软件开发"、"产品管理"
- 避免过于宽泛的领域设置

**性格特征选择**
- `assertive`（果断型）：主动表达观点
- `collaborative`（协作型）：协调时发言
- `diplomatic`（外交型）：调解分歧
- `direct`（直接型）：直接表达观点
- `thoughtful`（深思型）：深思后发言

**LLM配置**
- 确保每个智能体都配置了有效的LLM
- 建议使用较新的模型以获得更好的判断能力

### 2. 讨论设置

**选择讨论模式**
- 自由讨论模式：体验新的发言意愿功能
- 主持人模式：传统的有序讨论

**话题选择**
- 选择有一定深度的话题，避免过于简单
- 确保话题与参与者的专业领域有一定关联

**参与者选择**
- 建议3-6个智能体参与
- 包含不同专业背景和性格特征的智能体

### 3. 主持人设置（可选）

如果使用主持人功能：
- 选择一个配置了LLM的智能体作为主持人
- 主持人将在无人发言时自动介入
- 主持人会分析讨论状态并做出决策

## 预期效果

### 讨论质量提升
- 减少重复和无关的发言
- 智能体发言更有针对性
- 讨论更加聚焦和高效

### 自然的讨论流程
- 智能体根据情况自主选择发言时机
- 避免强制轮流导致的不自然发言
- 主持人适时介入维持讨论活跃度

### 个性化表现
- 不同性格的智能体表现出不同的发言模式
- 专业相关度影响发言积极性
- 更真实的多人讨论体验

## 注意事项

### 1. LLM配置要求
- 所有参与的智能体都需要配置有效的LLM
- 建议使用相同或兼容的LLM提供商
- 确保API密钥有效且有足够的配额

### 2. 讨论话题
- 避免过于简单或过于复杂的话题
- 确保话题有足够的讨论空间
- 考虑参与者的专业背景匹配度

### 3. 性能考虑
- 发言意愿查询会增加LLM API调用次数
- 建议监控API使用量和成本
- 可以通过减少参与者数量来控制调用频率

## 故障排除

### 智能体不发言
1. 检查LLM配置是否正确
2. 确认话题与智能体专业领域的相关性
3. 查看控制台日志了解具体原因

### 讨论过早结束
1. 检查主持人配置
2. 调整话题的复杂度和开放性
3. 增加不同背景的参与者

### 发言判断不准确
1. 优化智能体的专业领域设置
2. 调整性格特征配置
3. 检查LLM模型的性能

## 技术细节

### API调用流程
1. 查询所有智能体的发言意愿
2. 从愿意发言的智能体中选择发言人
3. 如无人愿意发言，主持人介入分析
4. 根据分析结果决定继续或结束

### 判断因素
- 专业领域相关性
- 观点重复性检测
- 性格特征影响
- 讨论阶段分析

### 主持人决策
- 分析讨论完成度
- 评估参与者状态
- 决定结束或提出新问题
