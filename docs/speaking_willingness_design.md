# 智能体发言意愿判断机制设计

## 1. 判断因素

### 1.1 专业相关性判断
- **高相关性**：当前话题与智能体专业领域高度匹配
- **中等相关性**：话题部分涉及智能体专业领域
- **低相关性**：话题与智能体专业领域关联较少

### 1.2 观点重复性检测
- **新观点**：智能体有独特的新观点要表达
- **补充观点**：可以对已有观点进行有价值的补充
- **重复观点**：要表达的观点已被充分讨论

### 1.3 性格特征影响
- **assertive（果断型）**：更倾向于主动发言，表达明确观点
- **collaborative（协作型）**：倾向于在需要协调时发言
- **diplomatic（外交型）**：倾向于在有分歧时发言调和
- **direct（直接型）**：倾向于直接表达观点，不避讳争议
- **thoughtful（深思型）**：倾向于在深度思考后发言

### 1.4 讨论状态分析
- **话题开始阶段**：各专业背景智能体都有较高发言意愿
- **深入讨论阶段**：相关专业智能体发言意愿较高
- **争议阶段**：diplomatic和collaborative性格智能体发言意愿提高
- **收敛阶段**：thoughtful性格智能体倾向于总结发言

## 2. 判断提示词模板

### 2.1 基础判断模板
```
作为智能体{agent_name}，你需要判断在当前讨论情况下是否想要发言。

你的特征：
- 专业领域：{expertise}
- 思维方式：{thinking_style}
- 性格特征：{personality}

当前讨论主题：{topic}

最近的讨论内容：
{recent_messages}

请基于以下因素判断是否想要发言：
1. 这个话题是否与你的专业领域相关？
2. 你是否有新的观点或有价值的补充？
3. 根据你的性格特征，现在是否是合适的发言时机？
4. 是否有需要澄清或纠正的内容？

请回答：是否想要发言（是/否），并简要说明理由（不超过30字）。
格式：是/否 - 理由
```

### 2.2 性格特征专用提示
- **assertive**: "作为果断型性格，你倾向于主动表达明确观点"
- **collaborative**: "作为协作型性格，你倾向于在需要协调和合作时发言"
- **diplomatic**: "作为外交型性格，你倾向于在有分歧时发言调和"
- **direct**: "作为直接型性格，你倾向于直接表达观点，不避讳争议"
- **thoughtful**: "作为深思型性格，你倾向于在充分思考后发言"

## 3. 实现逻辑

### 3.1 判断流程
1. 构建个性化判断提示词
2. 调用智能体的LLM进行判断
3. 解析返回结果（是/否 + 理由）
4. 记录判断结果和理由

### 3.2 异常处理
- LLM调用失败：默认愿意发言
- 返回格式错误：尝试解析，失败则默认愿意发言
- 网络超时：默认愿意发言

### 3.3 性能优化
- 缓存最近的判断结果
- 批量查询多个智能体的发言意愿
- 设置合理的超时时间

## 4. 集成点

### 4.1 自由讨论模式
- 在`getNextSpeaker`函数中集成
- 先查询所有参与者的发言意愿
- 从愿意发言的智能体中选择

### 4.2 主持人处理
- 当所有智能体都不愿发言时触发
- 主持人分析讨论完成度
- 决定结束或提出新问题

### 4.3 UI反馈
- 显示智能体的发言意愿状态
- 显示不发言的理由
- 提供手动干预选项
