/**
 * Memory系统类型定义
 * 为每个智能体提供独立的消息记忆功能
 */

import { Message } from './index';

// 智能体记忆条目
export interface MemoryEntry {
  id: string;                    // 记忆条目ID
  messageId: string;             // 关联的消息ID
  content: string;               // 消息内容
  type: Message['type'];         // 消息类型
  timestamp: Date;               // 时间戳
  discussionId: string;          // 所属讨论ID
  isOwnMessage: boolean;         // 是否为自己发送的消息
  context?: string;              // 上下文信息（可选）
}

// 智能体记忆存储
export interface AgentMemory {
  agentId: string;               // 智能体ID
  discussionId: string;          // 讨论会话ID
  entries: MemoryEntry[];        // 记忆条目列表
  createdAt: Date;               // 创建时间
  lastUpdated: Date;             // 最后更新时间
  maxEntries: number;            // 最大记忆条目数量
}

// 记忆查询选项
export interface MemoryQueryOptions {
  limit?: number;                // 返回条目数量限制
  includeOwnMessages?: boolean;  // 是否包含自己的消息
  includeOthersMessages?: boolean; // 是否包含他人的消息
  messageTypes?: Message['type'][]; // 筛选消息类型
  timeRange?: {                  // 时间范围
    start?: Date;
    end?: Date;
  };
}

// 记忆统计信息
export interface MemoryStats {
  totalEntries: number;          // 总记忆条目数
  ownMessages: number;           // 自己的消息数
  othersMessages: number;        // 他人的消息数
  messageTypeDistribution: Record<Message['type'], number>; // 消息类型分布
  oldestEntry?: Date;            // 最早记忆时间
  newestEntry?: Date;            // 最新记忆时间
}

// LLM对话历史格式（用于替代buildConversationHistory）
export interface LLMConversationEntry {
  role: 'user' | 'assistant';    // LLM角色
  content: string;               // 消息内容
  timestamp?: Date;              // 时间戳（可选）
}

// 记忆服务接口
export interface IMemoryService {
  // 基础操作
  createMemory(agentId: string, discussionId: string): AgentMemory;
  getMemory(agentId: string, discussionId: string): AgentMemory | null;
  addEntry(agentId: string, discussionId: string, message: Message): void;
  clearMemory(agentId: string, discussionId: string): void;
  
  // 查询操作
  getEntries(agentId: string, discussionId: string, options?: MemoryQueryOptions): MemoryEntry[];
  getStats(agentId: string, discussionId: string): MemoryStats;
  
  // LLM集成
  buildConversationHistory(agentId: string, discussionId: string, options?: MemoryQueryOptions): LLMConversationEntry[];
  
  // 管理操作
  cleanup(discussionId: string): void; // 清理指定讨论的所有记忆
  getAllMemories(discussionId: string): Map<string, AgentMemory>; // 获取讨论中所有智能体的记忆
}

// 记忆配置
export interface MemoryConfig {
  maxEntriesPerAgent: number;    // 每个智能体最大记忆条目数
  defaultQueryLimit: number;     // 默认查询限制
  includeOwnMessages: boolean;   // 默认是否包含自己的消息
  includeOthersMessages: boolean; // 默认是否包含他人的消息
  autoCleanup: boolean;          // 是否自动清理过期记忆
}

// 默认记忆配置
export const DEFAULT_MEMORY_CONFIG: MemoryConfig = {
  maxEntriesPerAgent: 100,       // 每个智能体最多记住100条消息
  defaultQueryLimit: 20,         // 默认查询最近20条
  includeOwnMessages: true,      // 包含自己的消息
  includeOthersMessages: true,   // 包含他人的消息
  autoCleanup: true              // 自动清理
};
