/**
 * Memory服务 - 为每个智能体提供独立的消息记忆功能
 */

import { v4 as uuidv4 } from 'uuid';
import { Message } from '../types';
import {
  AgentMemory,
  MemoryEntry,
  MemoryQueryOptions,
  MemoryStats,
  LLMConversationEntry,
  IMemoryService,
  MemoryConfig,
  DEFAULT_MEMORY_CONFIG
} from '../types/memory';

export class MemoryService implements IMemoryService {
  private static instance: MemoryService;
  private memories: Map<string, AgentMemory> = new Map(); // key: agentId-discussionId
  private config: MemoryConfig;

  private constructor(config: MemoryConfig = DEFAULT_MEMORY_CONFIG) {
    this.config = config;
  }

  public static getInstance(config?: MemoryConfig): MemoryService {
    if (!MemoryService.instance) {
      MemoryService.instance = new MemoryService(config);
    }
    return MemoryService.instance;
  }

  // 生成记忆存储的唯一键
  private getMemoryKey(agentId: string, discussionId: string): string {
    return `${agentId}-${discussionId}`;
  }

  // 创建新的智能体记忆
  createMemory(agentId: string, discussionId: string): AgentMemory {
    const key = this.getMemoryKey(agentId, discussionId);
    
    if (this.memories.has(key)) {
      return this.memories.get(key)!;
    }

    const memory: AgentMemory = {
      agentId,
      discussionId,
      entries: [],
      createdAt: new Date(),
      lastUpdated: new Date(),
      maxEntries: this.config.maxEntriesPerAgent
    };

    this.memories.set(key, memory);
    return memory;
  }

  // 获取智能体记忆
  getMemory(agentId: string, discussionId: string): AgentMemory | null {
    const key = this.getMemoryKey(agentId, discussionId);
    return this.memories.get(key) || null;
  }

  // 添加记忆条目
  addEntry(agentId: string, discussionId: string, message: Message): void {
    let memory = this.getMemory(agentId, discussionId);
    
    if (!memory) {
      memory = this.createMemory(agentId, discussionId);
    }

    // 检查是否已存在相同的消息
    const existingEntry = memory.entries.find(entry => entry.messageId === message.id);
    if (existingEntry) {
      return; // 避免重复添加
    }

    const entry: MemoryEntry = {
      id: uuidv4(),
      messageId: message.id,
      content: message.content,
      type: message.type,
      timestamp: message.timestamp,
      discussionId,
      isOwnMessage: message.agentId === agentId,
      context: this.generateContext(message, memory.entries)
    };

    memory.entries.push(entry);
    memory.lastUpdated = new Date();

    // 如果超过最大条目数，删除最旧的条目
    if (memory.entries.length > memory.maxEntries) {
      memory.entries.shift();
    }

    this.memories.set(this.getMemoryKey(agentId, discussionId), memory);
  }

  // 生成上下文信息
  private generateContext(_message: Message, existingEntries: MemoryEntry[]): string {
    const recentEntries = existingEntries.slice(-3); // 最近3条记忆
    if (recentEntries.length === 0) {
      return '讨论开始';
    }

    const contextParts = recentEntries.map(entry => 
      `${entry.isOwnMessage ? '我' : '他人'}说: ${entry.content.substring(0, 30)}...`
    );

    return `前文: ${contextParts.join(' | ')}`;
  }

  // 清理记忆
  clearMemory(agentId: string, discussionId: string): void {
    const key = this.getMemoryKey(agentId, discussionId);
    this.memories.delete(key);
  }

  // 查询记忆条目
  getEntries(agentId: string, discussionId: string, options: MemoryQueryOptions = {}): MemoryEntry[] {
    const memory = this.getMemory(agentId, discussionId);
    if (!memory) {
      return [];
    }

    let entries = [...memory.entries];

    // 应用筛选条件
    if (options.includeOwnMessages === false) {
      entries = entries.filter(entry => !entry.isOwnMessage);
    }
    if (options.includeOthersMessages === false) {
      entries = entries.filter(entry => entry.isOwnMessage);
    }
    if (options.messageTypes && options.messageTypes.length > 0) {
      entries = entries.filter(entry => options.messageTypes!.includes(entry.type));
    }
    if (options.timeRange) {
      if (options.timeRange.start) {
        entries = entries.filter(entry => entry.timestamp >= options.timeRange!.start!);
      }
      if (options.timeRange.end) {
        entries = entries.filter(entry => entry.timestamp <= options.timeRange!.end!);
      }
    }

    // 应用数量限制
    const limit = options.limit || this.config.defaultQueryLimit;
    return entries.slice(-limit); // 返回最近的条目
  }

  // 获取记忆统计信息
  getStats(agentId: string, discussionId: string): MemoryStats {
    const memory = this.getMemory(agentId, discussionId);
    if (!memory) {
      return {
        totalEntries: 0,
        ownMessages: 0,
        othersMessages: 0,
        messageTypeDistribution: {
          statement: 0,
          question: 0,
          agreement: 0,
          disagreement: 0
        }
      };
    }

    const stats: MemoryStats = {
      totalEntries: memory.entries.length,
      ownMessages: memory.entries.filter(e => e.isOwnMessage).length,
      othersMessages: memory.entries.filter(e => !e.isOwnMessage).length,
      messageTypeDistribution: {
        statement: 0,
        question: 0,
        agreement: 0,
        disagreement: 0
      }
    };

    // 计算消息类型分布
    memory.entries.forEach(entry => {
      stats.messageTypeDistribution[entry.type]++;
    });

    // 计算时间范围
    if (memory.entries.length > 0) {
      stats.oldestEntry = memory.entries[0].timestamp;
      stats.newestEntry = memory.entries[memory.entries.length - 1].timestamp;
    }

    return stats;
  }

  // 构建LLM对话历史（替代buildConversationHistory）
  buildConversationHistory(
    agentId: string, 
    discussionId: string, 
    options: MemoryQueryOptions = {}
  ): LLMConversationEntry[] {
    const entries = this.getEntries(agentId, discussionId, {
      limit: options.limit || 10, // 默认最近10条
      includeOwnMessages: options.includeOwnMessages ?? true,
      includeOthersMessages: options.includeOthersMessages ?? true,
      ...options
    });

    return entries.map(entry => ({
      role: entry.isOwnMessage ? 'assistant' : 'user',
      content: entry.isOwnMessage 
        ? `我：${entry.content}` 
        : `其他参与者：${entry.content}`,
      timestamp: entry.timestamp
    }));
  }

  // 清理指定讨论的所有记忆
  cleanup(discussionId: string): void {
    const keysToDelete: string[] = [];
    
    for (const [key, memory] of this.memories.entries()) {
      if (memory.discussionId === discussionId) {
        keysToDelete.push(key);
      }
    }

    keysToDelete.forEach(key => this.memories.delete(key));
  }

  // 获取讨论中所有智能体的记忆
  getAllMemories(discussionId: string): Map<string, AgentMemory> {
    const result = new Map<string, AgentMemory>();

    for (const [, memory] of this.memories.entries()) {
      if (memory.discussionId === discussionId) {
        result.set(memory.agentId, memory);
      }
    }

    return result;
  }

  // 更新配置
  updateConfig(newConfig: Partial<MemoryConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  // 获取当前配置
  getConfig(): MemoryConfig {
    return { ...this.config };
  }

  // 获取服务状态信息
  getServiceInfo() {
    return {
      totalMemories: this.memories.size,
      config: this.config,
      memoryKeys: Array.from(this.memories.keys())
    };
  }
}

// 导出单例实例
export const memoryService = MemoryService.getInstance();
