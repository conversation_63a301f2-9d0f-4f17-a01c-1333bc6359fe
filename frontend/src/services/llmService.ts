import { LLMConfig, LLMRequest, LLMResponse, Agent, Message, Discussion } from '../types';
import { memoryService } from './memoryService';

// LLM服务类
export class LLMService {
  private static instance: LLMService;
  
  public static getInstance(): LLMService {
    if (!LLMService.instance) {
      LLMService.instance = new LLMService();
    }
    return LLMService.instance;
  }

  // 调用LLM API
  async callLLM(config: LLMConfig, request: LLMRequest): Promise<LLMResponse> {
    try {
      const response = await this.makeAPICall(config, request);
      return response;
    } catch (error) {
      console.error('LLM API调用失败:', error);
      throw new Error(`LLM调用失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  // 生成简单响应（用于主持人功能）
  async generateResponse(config: LLMConfig, prompt: string): Promise<string> {
    const request: LLMRequest = {
      messages: [
        { role: 'user', content: prompt }
      ],
      temperature: config.temperature || 0.7,
      maxTokens: config.maxTokens || 500,
      model: config.model
    };

    const response = await this.callLLM(config, request);
    return response.content;
  }

  // 检查智能体发言意愿
  async checkSpeakingWillingness(
    agent: Agent,
    discussion: Discussion,
    recentMessages: Message[] = []
  ): Promise<{ willing: boolean; reason: string }> {
    if (!agent.llmConfig) {
      // 如果没有配置LLM，默认愿意发言
      return { willing: true, reason: '默认愿意发言' };
    }

    try {
      const prompt = this.buildWillingnessPrompt(agent, discussion, recentMessages);
      const response = await this.generateResponse(agent.llmConfig, prompt);
      console.log(`智能体 ${agent.name} 发言意愿查询结果:`, response);
      return this.parseWillingnessResponse(response);
    } catch (error) {
      console.error(`智能体 ${agent.name} 发言意愿查询失败:`, error);
      // 出错时默认愿意发言
      return { willing: true, reason: '查询失败，默认愿意发言' };
    }
  }

  // 生成智能体消息
  async generateAgentMessage(
    agent: Agent,
    discussion: Discussion,
    topic: string,
    _lastMessages: Message[] = [], // 保留参数以保持向后兼容性，但不使用
    isModerator: boolean = false
  ): Promise<string> {
    const systemPrompt = this.buildSystemPrompt(agent, topic, isModerator);

    // 使用智能体的独立memory替代buildConversationHistory
    const conversationHistory = memoryService.buildConversationHistory(
      agent.id,
      discussion.id,
      {
        limit: 10, // 获取最近10条记忆
        includeOwnMessages: true,
        includeOthersMessages: true
      }
    );

    const request: LLMRequest = {
      messages: [
        { role: 'system', content: systemPrompt },
        ...conversationHistory,
        { role: 'user', content: `请基于当前讨论情况，就"${topic}"这个话题发表你的观点。` }
      ],
      temperature: agent.llmConfig.temperature || 0.7,
      maxTokens: agent.llmConfig.maxTokens || 500,
      model: agent.llmConfig.model
    };

    const response = await this.callLLM(agent.llmConfig, request);
    return response.content;
  }

  // 构建系统提示词
  private buildSystemPrompt(agent: Agent, topic: string, isModerator: boolean = false): string {
    const roleDescription = isModerator
      ? `你是一个名为"${agent.name}"的智能体，正在作为主持人主持关于"${topic}"的讨论。`
      : `你是一个名为"${agent.name}"的智能体，正在参与关于"${topic}"的讨论。`;

    const roleSpecificGuidelines = isModerator
      ? `作为主持人，你的职责包括：
1. 引导讨论方向，确保讨论围绕主题进行
2. 平衡各方观点，促进建设性对话
3. 适时总结要点，推进讨论进展
4. 维持讨论秩序，化解可能的冲突
5. 在适当时机推动达成共识`
      : `请根据你的专业背景和性格特征参与讨论，保持角色一致性。你的回复应该：
1. 体现你的专业知识和思维方式
2. 符合你的性格特征
3. 简洁明了，通常在100字以内
4. 针对讨论话题提供有价值的新观点`;

    const basePrompt = `${roleDescription}

你的特征：
- 专业领域：${agent.expertise.join('、')}
- 思维方式：${agent.thinkingStyle}
- 性格特征：${agent.personality}
- 可用工具：${agent.tools.join('、')}

${roleSpecificGuidelines}

${agent.llmConfig.systemPrompt || ''}`;

    return basePrompt;
  }

  // 构建对话历史 (已废弃，由Memory系统替代)
  // private buildConversationHistory(messages: Message[], currentAgentId: string): Array<{role: 'user' | 'assistant', content: string}> {
  //   const recentMessages = messages.slice(-10); // 只取最近10条消息
  //
  //   return recentMessages.map(msg => ({
  //     role: msg.agentId === currentAgentId ? 'assistant' : 'user',
  //     content: `${msg.agentId === currentAgentId ? '我' : '其他参与者'}：${msg.content}`
  //   }));
  // }

  // 实际的API调用
  private async makeAPICall(config: LLMConfig, request: LLMRequest): Promise<LLMResponse> {
    const url = this.getAPIUrl(config);
    const headers = this.getAPIHeaders(config);
    const body = this.formatRequestBody(config, request);

    const response = await fetch(url, {
      method: 'POST',
      headers,
      body: JSON.stringify(body)
    });

    if (!response.ok) {
      throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return this.parseResponse(config, data);
  }

  // 获取API URL
  private getAPIUrl(config: LLMConfig): string {
    if (config.baseURL) {
      return `${config.baseURL}/chat/completions`;
    }

    switch (config.provider) {
      case 'openai':
        return 'https://api.openai.com/v1/chat/completions';
      case 'anthropic':
        return 'https://api.anthropic.com/v1/messages';
      case 'azure':
        return `${config.baseURL}/openai/deployments/${config.model}/chat/completions?api-version=2023-12-01-preview`;
      default:
        throw new Error(`不支持的提供商: ${config.provider}`);
    }
  }

  // 获取API请求头
  private getAPIHeaders(config: LLMConfig): Record<string, string> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json'
    };

    switch (config.provider) {
      case 'openai':
      case 'azure':
      case 'custom':
        headers['Authorization'] = `Bearer ${config.apiKey}`;
        break;
      case 'anthropic':
        headers['x-api-key'] = config.apiKey;
        headers['anthropic-version'] = '2023-06-01';
        break;
    }

    return headers;
  }

  // 格式化请求体
  private formatRequestBody(config: LLMConfig, request: LLMRequest): any {
    switch (config.provider) {
      case 'anthropic':
        return {
          model: request.model || config.model,
          max_tokens: request.maxTokens || 1000,
          temperature: request.temperature || 0.7,
          messages: request.messages.filter(m => m.role !== 'system'),
          system: request.messages.find(m => m.role === 'system')?.content || ''
        };
      default:
        return {
          model: request.model || config.model,
          messages: request.messages,
          temperature: request.temperature || 0.7,
          max_tokens: request.maxTokens || 1000
        };
    }
  }

  // 解析响应
  private parseResponse(config: LLMConfig, data: any): LLMResponse {
    switch (config.provider) {
      case 'anthropic':
        return {
          content: data.content[0]?.text || '',
          usage: data.usage ? {
            promptTokens: data.usage.input_tokens,
            completionTokens: data.usage.output_tokens,
            totalTokens: data.usage.input_tokens + data.usage.output_tokens
          } : undefined,
          model: data.model
        };
      default:
        return {
          content: data.choices[0]?.message?.content || '',
          usage: data.usage ? {
            promptTokens: data.usage.prompt_tokens,
            completionTokens: data.usage.completion_tokens,
            totalTokens: data.usage.total_tokens
          } : undefined,
          model: data.model
        };
    }
  }

  // 测试LLM配置
  async testLLMConfig(config: LLMConfig): Promise<boolean> {
    try {
      const testRequest: LLMRequest = {
        messages: [
          { role: 'user', content: '请回复"测试成功"' }
        ],
        temperature: 0.1,
        maxTokens: 10
      };

      await this.callLLM(config, testRequest);
      return true;
    } catch (error) {
      console.error('LLM配置测试失败:', error);
      return false;
    }
  }

  // 构建发言意愿判断提示词
  private buildWillingnessPrompt(agent: Agent, discussion: Discussion, recentMessages: Message[]): string {
    const personalityPrompts = {
      'assertive': '作为果断型性格，你倾向于主动表达明确观点',
      'collaborative': '作为协作型性格，你倾向于在需要协调和合作时发言',
      'diplomatic': '作为外交型性格，你倾向于在有分歧时发言调和',
      'direct': '作为直接型性格，你倾向于直接表达观点，不避讳争议',
      'thoughtful': '作为深思型性格，你倾向于在充分思考后发言'
    };

    const personalityHint = personalityPrompts[agent.personality as keyof typeof personalityPrompts] || '';

    const recentContent = recentMessages.slice(-5).map(msg =>
      `- ${msg.content}`
    ).join('\n');

    return `作为智能体${agent.name}，你需要判断在当前讨论情况下是否想要发言。

你的特征：
- 专业领域：${agent.expertise.join('、')}
- 思维方式：${agent.thinkingStyle}
- 性格特征：${agent.personality}

${personalityHint}

当前讨论主题：${discussion.topic}

最近的讨论内容：
${recentContent}

请基于以下因素判断是否想要发言：
1. 这个话题是否与你的专业领域相关？
2. 你是否有新的观点或有价值的补充？
3. 根据你的性格特征，现在是否是合适的发言时机？
4. 是否有需要澄清或纠正的内容？

请回答：是否想要发言（是/否），并简要说明理由（不超过30字）。
格式：是/否 - 理由`;
  }

  // 解析发言意愿响应
  private parseWillingnessResponse(response: string): { willing: boolean; reason: string } {
    try {
      const trimmed = response.trim();

      // 尝试解析标准格式：是/否 - 理由
      const match = trimmed.match(/^(是|否)\s*[-–—]\s*(.+)$/);
      if (match) {
        return {
          willing: match[1] === '是',
          reason: match[2].trim()
        };
      }

      // 尝试其他可能的格式
      if (trimmed.startsWith('是')) {
        return {
          willing: true,
          reason: trimmed.replace(/^是\s*[-–—]?\s*/, '') || '愿意发言'
        };
      }

      if (trimmed.startsWith('否')) {
        return {
          willing: false,
          reason: trimmed.replace(/^否\s*[-–—]?\s*/, '') || '不愿意发言'
        };
      }

      // 如果无法解析，默认愿意发言
      return {
        willing: true,
        reason: '格式解析失败，默认愿意发言'
      };
    } catch (error) {
      console.error('解析发言意愿响应失败:', error);
      return {
        willing: true,
        reason: '解析失败，默认愿意发言'
      };
    }
  }
}

// 导出单例实例
export const llmService = LLMService.getInstance();
