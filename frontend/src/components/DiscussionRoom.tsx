import { useEffect, useRef, useState } from 'react';
import { useApp } from '../context/AppContext';
import {
  generateMessage,
  calculateConsensus,
  getNextSpeakerWithWillingness,
  handleNoWillingSpeakers,
  generateConsensus,
  generateModeratorSummary,
  calculateTopicRelevance,
  selectNextSpeaker,
  generateModeratorIntervention,
  shouldTerminateDiscussion
} from '../utils/aiLogic';
import { Message, Discussion } from '../types';
import {
  MessageSquare,
  Users,
  TrendingUp,
  StopCircle,
  CheckCircle,
  HelpCircle,
  ThumbsUp,
  ThumbsDown,
  BarChart3,
  Clock,
  AlertTriangle,
  UserX,
  Target
} from 'lucide-react';
export default function DiscussionRoom() {
  const { state, dispatch, endDiscussion, sendMessage, setCurrentDiscussion } = useApp();
  const [isSimulating, setIsSimulating] = useState(false);
  const [lastEndedDiscussion, setLastEndedDiscussion] = useState<Discussion | null>(null);
  const [showFullMessages, setShowFullMessages] = useState(false);
  const [discussionStats, setDiscussionStats] = useState({
    totalMessages: 0,
    consensusScore: 0,
    activeTime: 0,
  });

  // 主持人相关状态
  const [moderatorSummary, setModeratorSummary] = useState<string>('');
  const [topicRelevance, setTopicRelevance] = useState<number>(1.0);
  const [lastSummaryMessageCount, setLastSummaryMessageCount] = useState(0);
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const discussionTimer = useRef<number | null>(null);
  const simulationTimer = useRef<number | null>(null);
  const { currentDiscussion } = state;
  useEffect(() => {
    // 只有当讨论状态为 'active' 且 isDiscussionActive 为 true 时才启动模拟
    // 这确保了历史讨论（isDiscussionActive = false）不会启动模拟
    if (currentDiscussion && currentDiscussion.status === 'active' && state.isDiscussionActive) {
      console.log('启动讨论模拟 - 讨论ID:', currentDiscussion.id, '状态:', currentDiscussion.status, '活跃:', state.isDiscussionActive);
      setIsSimulating(true);
      startDiscussionSimulation();
    } else {
      // 如果讨论不活跃，立即停止模拟并清理定时器
      console.log('停止讨论模拟 - 讨论ID:', currentDiscussion?.id, '状态:', currentDiscussion?.status, '活跃:', state.isDiscussionActive);
      setIsSimulating(false);
      if (simulationTimer.current) {
        clearTimeout(simulationTimer.current);
        simulationTimer.current = null;
      }
      if (discussionTimer.current) {
        clearInterval(discussionTimer.current);
        discussionTimer.current = null;
      }
    }
    return () => {
      if (discussionTimer.current) clearInterval(discussionTimer.current);
      if (simulationTimer.current) clearTimeout(simulationTimer.current);
    };
  }, [currentDiscussion, state.isDiscussionActive]);

  // 监听模拟状态变化，确保停止时清理定时器
  useEffect(() => {
    if (!isSimulating) {
      if (simulationTimer.current) {
        clearTimeout(simulationTimer.current);
        simulationTimer.current = null;
      }
    }
  }, [isSimulating]);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [currentDiscussion?.messages]);
  useEffect(() => {
    if (currentDiscussion) {
      const consensusScore = calculateConsensus(currentDiscussion.messages, state.agents);

      // 计算讨论时长
      let activeTime: number;
      if (state.isDiscussionActive) {
        // 活跃讨论：从创建时间到现在
        activeTime = Math.floor((Date.now() - new Date(currentDiscussion.createdAt).getTime()) / 1000);
      } else {
        // 历史讨论：从创建时间到最后一条消息时间
        const lastMessage = currentDiscussion.messages[currentDiscussion.messages.length - 1];
        if (lastMessage) {
          activeTime = Math.floor((new Date(lastMessage.timestamp).getTime() - new Date(currentDiscussion.createdAt).getTime()) / 1000);
        } else {
          activeTime = 0;
        }
      }

      setDiscussionStats({
        totalMessages: currentDiscussion.messages.length,
        consensusScore,
        activeTime,
      });

      // 只在活跃讨论中更新共识度
      if (state.isDiscussionActive) {
        dispatch({
          type: 'UPDATE_CONSENSUS',
          payload: { consensusScore }
        });
        // 检查是否达成共识
        if (consensusScore > 80 && currentDiscussion.status === 'active') {
          const consensus = generateConsensus(currentDiscussion.messages, currentDiscussion.topic);
          dispatch({
            type: 'UPDATE_CONSENSUS',
            payload: { consensusScore, consensus }
          });
          setIsSimulating(false);

          // 清理定时器
          if (simulationTimer.current) {
            clearTimeout(simulationTimer.current);
            simulationTimer.current = null;
          }

          // 自动结束讨论
          setTimeout(async () => {
            await handleEndDiscussionWithSummary('达成共识');
          }, 2000); // 延迟2秒让用户看到共识结果
        }
      }
    }
  }, [currentDiscussion?.messages]);

  // 监听讨论状态变化，保存结束的讨论信息
  useEffect(() => {
    if (!state.isDiscussionActive && state.currentDiscussion === null && lastEndedDiscussion === null) {
      // 如果讨论刚刚结束，从历史记录中获取最新的讨论
      if (state.allDiscussions.length > 0) {
        const latestDiscussion = state.allDiscussions[0];
        if (latestDiscussion.status === 'ended') {
          setLastEndedDiscussion(latestDiscussion);
        }
      }
    } else if (state.isDiscussionActive && state.currentDiscussion) {
      // 如果开始新讨论，清除之前结束的讨论信息
      setLastEndedDiscussion(null);
      setShowFullMessages(false);
    }
  }, [state.isDiscussionActive, state.currentDiscussion, state.allDiscussions, lastEndedDiscussion]);

  const startDiscussionSimulation = () => {
    // 确保只有活跃的讨论才能启动模拟
    if (!currentDiscussion || currentDiscussion.status !== 'active' || !state.isDiscussionActive) {
      console.log('无法启动模拟 - 讨论状态:', currentDiscussion?.status, '活跃状态:', state.isDiscussionActive);
      return;
    }

    console.log('开始讨论模拟 - 讨论ID:', currentDiscussion.id);
    const participants = currentDiscussion.participants;
    const participantAgents = state.agents.filter(agent => participants.includes(agent.id));
    const moderator = currentDiscussion.moderatorId ?
      state.agents.find(agent => agent.id === currentDiscussion.moderatorId) : null;

    const simulateMessage = async () => {
      // 检查讨论状态 - 使用最新的状态
      if (!state.currentDiscussion || state.currentDiscussion.status !== 'active' || !state.isDiscussionActive) {
        console.log('停止模拟消息 - 状态检查失败:', {
          hasDiscussion: !!state.currentDiscussion,
          status: state.currentDiscussion?.status,
          isActive: state.isDiscussionActive
        });
        setIsSimulating(false);
        // 清理定时器
        if (simulationTimer.current) {
          clearTimeout(simulationTimer.current);
          simulationTimer.current = null;
        }
        return;
      }

      // 使用最新的讨论状态
      const latestDiscussion = state.currentDiscussion;

      // 主持人功能：检查是否需要总结
      if (moderator && moderator.moderatorConfig) {
        const config = moderator.moderatorConfig;
        const messagesSinceLastSummary = latestDiscussion.messages.length - lastSummaryMessageCount;

        if (messagesSinceLastSummary >= config.summaryFrequency) {
          try {
            const summary = await generateModeratorSummary(
              moderator,
              latestDiscussion,
              latestDiscussion.messages.slice(-config.summaryFrequency)
            );
            setModeratorSummary(summary);
            setLastSummaryMessageCount(latestDiscussion.messages.length);

            // 将总结保存到讨论对象的 moderatorSummaries 数组中
            const updatedSummaries = [...latestDiscussion.moderatorSummaries, summary];
            dispatch({
              type: 'UPDATE_CONSENSUS',
              payload: {
                consensusScore: latestDiscussion.consensusScore,
                moderatorSummaries: updatedSummaries
              }
            });
          } catch (error) {
            console.error('主持人总结生成失败:', error);
          }
        }

        // 主持人功能：检查话题相关性
        if (latestDiscussion.messages.length > 0) {
          try {
            const relevance = await calculateTopicRelevance(
              moderator,
              latestDiscussion,
              latestDiscussion.messages.slice(-3)
            );
            setTopicRelevance(relevance);

            // 如果相关性过低，主持人干预
            if (relevance < config.interventionThreshold) {
              const newInterventionCount = (latestDiscussion.moderatorInterventions || 0) + 1;

              // 检查是否达到最大干预次数
              if (newInterventionCount >= config.maxInterventions && config.maxInterventions > 0) {
                console.log('达到最大干预次数，主持人终止讨论');
                await handleEndDiscussionWithSummary('偏离话题');
                return;
              }

              const intervention = await generateModeratorIntervention(
                moderator,
                latestDiscussion,
                'off_topic'
              );

              // 在发送主持人干预消息前检查讨论状态
              if (!state.currentDiscussion || state.currentDiscussion.status !== 'active' || !state.isDiscussionActive) {
                console.log('主持人干预生成完成但讨论已结束，取消发送');
                return;
              }

              sendMessage(intervention, moderator.id, 'statement');

              // 更新干预次数
              dispatch({
                type: 'UPDATE_CONSENSUS',
                payload: {
                  consensusScore: latestDiscussion.consensusScore,
                  moderatorInterventions: newInterventionCount
                }
              });

              // 再次检查讨论状态后再继续 - 使用最新状态检查
              if (state.isDiscussionActive && state.currentDiscussion?.status === 'active' && isSimulating) {
                const delay = Math.random() * 3000 + 2000;
                simulationTimer.current = setTimeout(simulateMessage, delay);
              } else {
                setIsSimulating(false);
              }
              return;
            }
          } catch (error) {
            console.error('话题相关性检查失败:', error);
          }
        }

        // 主持人功能：检查是否应该终止讨论
        try {
          const terminationCheck = await shouldTerminateDiscussion(moderator, latestDiscussion, config);
          if (terminationCheck.shouldTerminate) {
            console.log('主持人决定终止讨论:', terminationCheck.reason);
            await handleEndDiscussionWithSummary(terminationCheck.reason || '主持人终止');
            return;
          }
        } catch (error) {
          console.error('讨论终止检查失败:', error);
        }
      }

      // 选择下一个发言人
      let nextSpeakerId: string | null = null;
      let willingnessResults: Array<{ agentId: string; willing: boolean; reason: string }> = [];

      if (moderator && latestDiscussion.mode === 'moderator') {
        // 主持人模式：保持原有逻辑，不受发言意愿限制
        nextSpeakerId = await selectNextSpeaker(
          moderator,
          latestDiscussion,
          participantAgents,
          latestDiscussion.messages
        );
      } else {
        // 自由讨论模式：使用发言意愿查询
        const result = await getNextSpeakerWithWillingness(
          participantAgents,
          latestDiscussion,
          latestDiscussion.messages
        );
        nextSpeakerId = result.speakerId;
        willingnessResults = result.willingnessResults;

        // 如果没有智能体愿意发言，让主持人处理
        if (!nextSpeakerId && moderator) {
          console.log('没有智能体愿意发言，主持人介入处理');
          try {
            const moderatorDecision = await handleNoWillingSpeakers(
              moderator,
              latestDiscussion,
              willingnessResults
            );

            if (moderatorDecision.action === 'terminate') {
              console.log('主持人决定结束讨论:', moderatorDecision.message);
              await handleEndDiscussionWithSummary('无人发言');
              return;
            } else {
              // 主持人提出新问题，继续讨论
              console.log('主持人提出新问题继续讨论:', moderatorDecision.message);

              // 在发送主持人消息前检查讨论状态
              if (!state.currentDiscussion || state.currentDiscussion.status !== 'active' || !state.isDiscussionActive) {
                console.log('主持人消息生成完成但讨论已结束，取消发送');
                return;
              }

              sendMessage(moderatorDecision.message, moderator.id, 'statement');

              // 继续下一轮讨论
              if (state.isDiscussionActive && state.currentDiscussion?.status === 'active' && isSimulating) {
                const delay = Math.random() * 3000 + 2000;
                simulationTimer.current = setTimeout(simulateMessage, delay);
              } else {
                setIsSimulating(false);
              }
              return;
            }
          } catch (error) {
            console.error('主持人处理无人发言失败:', error);
            await handleEndDiscussionWithSummary('处理异常');
            return;
          }
        } else if (!nextSpeakerId) {
          // 没有主持人且没有人愿意发言，直接结束讨论
          console.log('没有主持人且无人愿意发言，讨论结束');
          await handleEndDiscussionWithSummary('无人发言');
          return;
        }
      }

      if (nextSpeakerId) {
        const speaker = participantAgents.find(agent => agent.id === nextSpeakerId);
        if (speaker) {
          try {
            // 判断当前发言者是否是主持人
            const isModerator = !!(moderator && speaker.id === moderator.id);
            const messageContent = await generateMessage(
              speaker,
              latestDiscussion,
              latestDiscussion.topic,
              latestDiscussion.messages.slice(-5),
              isModerator
            );

            // 在发送消息前再次检查讨论状态
            if (!state.currentDiscussion || state.currentDiscussion.status !== 'active' || !state.isDiscussionActive) {
              console.log('消息生成完成但讨论已结束，取消发送:', speaker.name);
              return;
            }

            const messageType = determineMessageType(messageContent);
            sendMessage(messageContent, speaker.id, messageType);
          } catch (error) {
            console.error('生成消息失败:', error);
          }
        }
      }

      // 在继续下一条消息前再次检查讨论状态 - 使用最新状态检查
      if (state.isDiscussionActive && state.currentDiscussion?.status === 'active' && isSimulating) {
        const delay = Math.random() * 3000 + 2000; // 2-5秒随机间隔
        simulationTimer.current = setTimeout(simulateMessage, delay);
      } else {
        setIsSimulating(false);
        // 确保清理定时器
        if (simulationTimer.current) {
          clearTimeout(simulationTimer.current);
          simulationTimer.current = null;
        }
      }
    };

    // 开始第一条消息
    const initialDelay = Math.random() * 2000 + 1000; // 1-3秒延迟
    simulationTimer.current = setTimeout(simulateMessage, initialDelay);
  };
  const determineMessageType = (content: string): Message['type'] => {
    if (content.includes('我赞同') || content.includes('我同意') || content.includes('这个想法很好')) {
      return 'agreement';
    }
    if (content.includes('但是') || content.includes('我认为') || content.includes('不同的看法')) {
      return 'disagreement';
    }
    if (content.includes('？') || content.includes('我们') || content.includes('如何')) {
      return 'question';
    }
    return 'statement';
  };
  // 通用的讨论结束处理函数
  const handleEndDiscussionWithSummary = async (reason: string) => {
    if (!currentDiscussion) return;

    // 获取主持人的最后一次总结
    const lastModeratorSummary = currentDiscussion.moderatorSummaries.length > 0
      ? currentDiscussion.moderatorSummaries[currentDiscussion.moderatorSummaries.length - 1]
      : moderatorSummary || ''; // 如果数组为空，使用当前状态中的总结

    // 保存当前讨论信息，以便在结束后显示
    setLastEndedDiscussion({
      ...currentDiscussion,
      status: 'ended',
      endReason: reason,
      finalModeratorSummary: lastModeratorSummary,
      endedAt: new Date() // 记录结束时间
    });

    endDiscussion(reason);
  };

  const handleEndDiscussion = async () => {
    console.log('用户点击结束讨论按钮');

    // 立即停止模拟
    setIsSimulating(false);

    // 清理所有定时器
    if (simulationTimer.current) {
      clearTimeout(simulationTimer.current);
      simulationTimer.current = null;
    }
    if (discussionTimer.current) {
      clearInterval(discussionTimer.current);
      discussionTimer.current = null;
    }

    // 立即调用结束讨论，这会立即更新状态
    await handleEndDiscussionWithSummary('手动终止');

    console.log('讨论结束处理完成');
  };
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // 获取结束原因的样式
  const getEndReasonStyle = (reason: string) => {
    switch (reason) {
      case '达成共识':
        return 'bg-green-50 border-green-200 text-green-800';
      case '手动终止':
        return 'bg-blue-50 border-blue-200 text-blue-800';
      case '偏离话题':
        return 'bg-orange-50 border-orange-200 text-orange-800';
      case '主持人终止':
        return 'bg-purple-50 border-purple-200 text-purple-800';
      case '时间超限':
        return 'bg-yellow-50 border-yellow-200 text-yellow-800';
      case '消息数量达到上限':
        return 'bg-indigo-50 border-indigo-200 text-indigo-800';
      default:
        return 'bg-gray-50 border-gray-200 text-gray-800';
    }
  };

  // 获取结束原因的图标
  const getEndReasonIcon = (reason: string) => {
    switch (reason) {
      case '达成共识':
        return <CheckCircle size={16} className="text-green-600" />;
      case '手动终止':
        return <StopCircle size={16} className="text-blue-600" />;
      case '偏离话题':
        return <AlertTriangle size={16} className="text-orange-600" />;
      case '主持人终止':
        return <UserX size={16} className="text-purple-600" />;
      case '时间超限':
        return <Clock size={16} className="text-yellow-600" />;
      case '消息数量达到上限':
        return <Target size={16} className="text-indigo-600" />;
      default:
        return <Clock size={16} className="text-gray-600" />;
    }
  };

  // 获取结束原因的友好文本
  const getEndReasonText = (reason: string) => {
    switch (reason) {
      case '达成共识':
        return '参与者达成共识';
      case '手动终止':
        return '用户手动结束';
      case '偏离话题':
        return '讨论偏离主题';
      case '主持人终止':
        return '主持人主动终止';
      case '时间超限':
        return '讨论时间超限';
      case '消息数量达到上限':
        return '消息数量达到上限';
      default:
        return reason || '未知原因';
    }
  };

  // 获取结束原因的详细描述
  const getEndReasonDescription = (reason: string) => {
    switch (reason) {
      case '达成共识':
        return '讨论参与者在话题上达成了足够的共识（共识度超过85%），系统自动结束了讨论';
      case '手动终止':
        return '用户主动点击"结束讨论"按钮终止了讨论';
      case '偏离话题':
        return '主持人检测到讨论内容偏离了原定话题，达到干预上限后终止讨论';
      case '主持人终止':
        return '主持人基于讨论情况（如活跃度、质量等）判断应该结束讨论';
      case '时间超限':
        return '讨论时间超过了预设的时间限制';
      case '消息数量达到上限':
        return '讨论消息数量达到了预设的上限';
      default:
        return '讨论因其他原因结束';
    }
  };
  if (!currentDiscussion) {
    // 如果有刚结束的讨论，显示讨论结束界面
    if (lastEndedDiscussion) {
      const endedParticipants = state.agents.filter(agent =>
        lastEndedDiscussion.participants.includes(agent.id)
      );

      return (
        <div className="h-full bg-gradient-to-br from-green-50 to-blue-50 w-full overflow-y-auto">
          <div className="max-w-7xl mx-auto p-6 space-y-6">
            {/* 头部信息 */}
            <div className="bg-white rounded-xl shadow-lg p-8 text-center">
              <CheckCircle size={64} className="text-green-500 mx-auto mb-4" />
              <h2 className="text-3xl font-bold text-gray-900 mb-4">讨论已结束</h2>

              {/* 突出显示话题 */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
                <h3 className="text-lg font-semibold text-blue-800 mb-2">讨论话题</h3>
                <p className="text-2xl font-bold text-blue-900">{lastEndedDiscussion.topic}</p>
              </div>

              {/* 显示结束原因 */}
              {lastEndedDiscussion.endReason && (
                <div className={`border rounded-lg p-4 mb-6 ${getEndReasonStyle(lastEndedDiscussion.endReason)}`}>
                  <div className="flex items-center gap-2 mb-2">
                    {getEndReasonIcon(lastEndedDiscussion.endReason)}
                    <h4 className="text-sm font-semibold">讨论结束原因</h4>
                  </div>
                  <p className="text-lg font-medium">{getEndReasonText(lastEndedDiscussion.endReason)}</p>
                  {getEndReasonDescription(lastEndedDiscussion.endReason) && (
                    <p className="text-sm mt-2 opacity-80">
                      {getEndReasonDescription(lastEndedDiscussion.endReason)}
                    </p>
                  )}
                  {/* 显示结束时间信息 */}
                  <div className="mt-3 pt-3 border-t border-current border-opacity-20">
                    <div className="flex items-center justify-between text-sm opacity-75">
                      <span>开始时间：{new Date(lastEndedDiscussion.createdAt).toLocaleString()}</span>
                      {lastEndedDiscussion.endedAt && (
                        <span>结束时间：{new Date(lastEndedDiscussion.endedAt).toLocaleString()}</span>
                      )}
                    </div>
                    {lastEndedDiscussion.endedAt && (
                      <div className="text-center mt-2">
                        <span className="text-sm font-medium">
                          总时长：{Math.floor((new Date(lastEndedDiscussion.endedAt).getTime() - new Date(lastEndedDiscussion.createdAt).getTime()) / 60000)} 分钟
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <div className="bg-blue-50 rounded-lg p-4">
                  <div className="text-2xl font-bold text-blue-600">{lastEndedDiscussion.messages.length}</div>
                  <div className="text-sm text-blue-800">总消息数</div>
                </div>
                <div className="bg-green-50 rounded-lg p-4">
                  <div className="text-2xl font-bold text-green-600">{lastEndedDiscussion.consensusScore}%</div>
                  <div className="text-sm text-green-800">共识度</div>
                </div>
                <div className="bg-purple-50 rounded-lg p-4">
                  <div className="text-2xl font-bold text-purple-600">{lastEndedDiscussion.participants.length}</div>
                  <div className="text-sm text-purple-800">参与者</div>
                </div>
                <div className="bg-orange-50 rounded-lg p-4">
                  <div className="text-2xl font-bold text-orange-600">
                    {Math.floor((new Date().getTime() - new Date(lastEndedDiscussion.createdAt).getTime()) / 60000)}
                  </div>
                  <div className="text-sm text-orange-800">讨论时长(分钟)</div>
                </div>
              </div>
            </div>

            {/* 最终结论 */}
            {lastEndedDiscussion.consensus && (
              <div className="bg-white rounded-xl shadow-lg p-8">
                <div className="flex items-center gap-3 mb-6">
                  <CheckCircle size={32} className="text-green-600" />
                  <h3 className="text-2xl font-bold text-gray-900">最终结论</h3>
                </div>
                <div className="bg-green-50 border border-green-200 rounded-lg p-6">
                  <p className="text-gray-800 text-lg leading-relaxed">{lastEndedDiscussion.consensus}</p>
                </div>
              </div>
            )}

            {/* 主持人最后一次总结 */}
            {lastEndedDiscussion.moderatorId && (
              <div className="bg-white rounded-xl shadow-lg p-8">
                <div className="flex items-center gap-3 mb-6">
                  <Users size={32} className="text-purple-600" />
                  <h3 className="text-2xl font-bold text-gray-900">主持人最后一次总结</h3>
                  <span className="bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm font-medium">
                    {(() => {
                      const moderator = state.agents.find(agent => agent.id === lastEndedDiscussion.moderatorId);
                      return moderator ? moderator.name : '主持人';
                    })()}
                  </span>
                </div>
                <div className="bg-purple-50 border border-purple-200 rounded-lg p-6">
                  <div className="flex items-center gap-2 mb-3">
                    <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                    <span className="text-sm text-purple-700 font-medium">讨论过程中的最后一次总结</span>
                  </div>
                  {lastEndedDiscussion.finalModeratorSummary ? (
                    <p className="text-gray-800 text-lg leading-relaxed whitespace-pre-wrap">
                      {lastEndedDiscussion.finalModeratorSummary}
                    </p>
                  ) : (
                    <div className="text-center py-4">
                      <p className="text-gray-500 text-base">
                        主持人在讨论过程中未生成总结
                      </p>
                      <p className="text-gray-400 text-sm mt-2">
                        可能是因为讨论时间较短或消息数量未达到总结阈值
                      </p>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* 消息类型统计 */}
            <div className="bg-white rounded-xl shadow-lg p-8">
              <div className="flex items-center gap-3 mb-6">
                <BarChart3 size={32} className="text-indigo-600" />
                <h3 className="text-2xl font-bold text-gray-900">消息类型分析</h3>
              </div>

              {(() => {
                const messageTypes = {
                  statement: { count: 0, label: '陈述', color: 'gray', icon: MessageSquare },
                  question: { count: 0, label: '提问', color: 'blue', icon: HelpCircle },
                  agreement: { count: 0, label: '赞同', color: 'green', icon: ThumbsUp },
                  disagreement: { count: 0, label: '反对', color: 'red', icon: ThumbsDown }
                };

                lastEndedDiscussion.messages.forEach(message => {
                  if (messageTypes[message.type]) {
                    messageTypes[message.type].count++;
                  }
                });

                const total = lastEndedDiscussion.messages.length;

                return (
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    {Object.entries(messageTypes).map(([type, data]) => {
                      const percentage = total > 0 ? Math.round((data.count / total) * 100) : 0;
                      const IconComponent = data.icon;

                      return (
                        <div key={type} className={`bg-${data.color}-50 rounded-lg p-4 text-center`}>
                          <IconComponent size={24} className={`text-${data.color}-600 mx-auto mb-2`} />
                          <div className={`text-2xl font-bold text-${data.color}-600`}>{data.count}</div>
                          <div className={`text-sm text-${data.color}-800 font-medium`}>{data.label}</div>
                          <div className={`text-xs text-${data.color}-600 mt-1`}>{percentage}%</div>
                        </div>
                      );
                    })}
                  </div>
                );
              })()}
            </div>

            {/* 参与者表现 */}
            <div className="bg-white rounded-xl shadow-lg p-8">
              <div className="flex items-center gap-3 mb-6">
                <Users size={32} className="text-blue-600" />
                <h3 className="text-2xl font-bold text-gray-900">参与者表现</h3>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {endedParticipants.map((agent) => {
                  const messageCount = lastEndedDiscussion.messages.filter(m => m.agentId === agent.id).length;
                  const agreementCount = lastEndedDiscussion.messages.filter(m => m.agentId === agent.id && m.type === 'agreement').length;
                  const disagreementCount = lastEndedDiscussion.messages.filter(m => m.agentId === agent.id && m.type === 'disagreement').length;
                  const questionCount = lastEndedDiscussion.messages.filter(m => m.agentId === agent.id && m.type === 'question').length;
                  const statementCount = lastEndedDiscussion.messages.filter(m => m.agentId === agent.id && m.type === 'statement').length;

                  return (
                    <div key={agent.id} className="bg-gray-50 rounded-lg p-4">
                      <div className="flex items-center gap-3 mb-3">
                        <img
                          src={agent.avatar}
                          alt={agent.name}
                          className="w-12 h-12 rounded-full object-cover"
                        />
                        <div>
                          <h4 className="font-semibold text-gray-900">{agent.name}</h4>
                          <p className="text-sm text-gray-500">{agent.expertise.slice(0, 2).join('、')}</p>
                        </div>
                      </div>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-600">总发言</span>
                          <span className="font-medium">{messageCount}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">陈述</span>
                          <span className="font-medium text-gray-600">{statementCount}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">提问</span>
                          <span className="font-medium text-blue-600">{questionCount}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">赞同</span>
                          <span className="font-medium text-green-600">{agreementCount}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">反对</span>
                          <span className="font-medium text-red-600">{disagreementCount}</span>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>

            {/* 完整消息记录 */}
            <div className="bg-white rounded-xl shadow-lg p-8">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center gap-3">
                  <MessageSquare size={32} className="text-purple-600" />
                  <h3 className="text-2xl font-bold text-gray-900">完整消息记录</h3>
                  <span className="bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm font-medium">
                    {lastEndedDiscussion.messages.length} 条消息
                  </span>
                </div>
                <button
                  onClick={() => setShowFullMessages(!showFullMessages)}
                  className="flex items-center gap-2 px-4 py-2 bg-purple-100 text-purple-700 rounded-lg hover:bg-purple-200 transition-colors"
                >
                  {showFullMessages ? '收起消息' : '展开消息'}
                  <BarChart3 size={16} />
                </button>
              </div>

              {showFullMessages ? (
                <div className="max-h-96 overflow-y-auto space-y-4 border border-gray-200 rounded-lg p-4">
                  {lastEndedDiscussion.messages.map((message) => {
                    const agent = endedParticipants.find(a => a.id === message.agentId);
                    return (
                      <MessageBubble
                        key={message.id}
                        message={message}
                        agent={agent}
                      />
                    );
                  })}
                </div>
              ) : (
                <div className="border border-gray-200 rounded-lg p-6 text-center">
                  <p className="text-gray-500 mb-4">
                    点击"展开消息"查看完整的讨论记录
                  </p>
                  <div className="flex justify-center gap-4 text-sm text-gray-400">
                    <span>首条消息：{new Date(lastEndedDiscussion.messages[0]?.timestamp).toLocaleString()}</span>
                    <span>•</span>
                    <span>末条消息：{new Date(lastEndedDiscussion.messages[lastEndedDiscussion.messages.length - 1]?.timestamp).toLocaleString()}</span>
                  </div>
                </div>
              )}
            </div>

            {/* 操作按钮 */}
            <div className="bg-white rounded-xl shadow-lg p-8">
              <div className="flex gap-4 justify-center">
                <button
                  onClick={() => {
                    // 立即停止任何模拟活动
                    setIsSimulating(false);
                    if (simulationTimer.current) {
                      clearTimeout(simulationTimer.current);
                      simulationTimer.current = null;
                    }
                    if (discussionTimer.current) {
                      clearInterval(discussionTimer.current);
                      discussionTimer.current = null;
                    }

                    // 复用历史记录中"转到聊天室"的逻辑
                    setCurrentDiscussion(lastEndedDiscussion);
                    setLastEndedDiscussion(null);
                    setShowFullMessages(false);
                  }}
                  className="px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
                >
                  回到讨论室
                </button>
                <button
                  onClick={() => {/* 可以添加查看详情的逻辑 */}}
                  className="px-8 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors font-medium"
                >
                  查看历史
                </button>
                <button
                  onClick={() => {
                    // 导出讨论记录
                    const discussionData = {
                      topic: lastEndedDiscussion.topic,
                      participants: endedParticipants.map(p => p.name),
                      messages: lastEndedDiscussion.messages.map(m => ({
                        speaker: endedParticipants.find(p => p.id === m.agentId)?.name || 'Unknown',
                        content: m.content,
                        type: m.type,
                        timestamp: new Date(m.timestamp).toLocaleString()
                      })),
                      consensus: lastEndedDiscussion.consensus,
                      consensusScore: lastEndedDiscussion.consensusScore,
                      duration: Math.floor((new Date().getTime() - new Date(lastEndedDiscussion.createdAt).getTime()) / 60000)
                    };

                    const blob = new Blob([JSON.stringify(discussionData, null, 2)], { type: 'application/json' });
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `讨论记录_${lastEndedDiscussion.topic}_${new Date().toISOString().split('T')[0]}.json`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);
                  }}
                  className="px-8 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium"
                >
                  导出记录
                </button>
              </div>

              <div className="mt-6 text-center">
                <p className="text-sm text-gray-500">
                  您可以通过导航栏的"创建讨论"按钮开始新的讨论
                </p>
              </div>
            </div>
          </div>
        </div>
      );
    }

    // 没有当前讨论也没有刚结束的讨论
    return (
      <div className="h-full bg-gradient-to-br from-gray-50 to-blue-50 w-full overflow-y-auto">
        <div className="discussion-container">
          <div className="bg-white rounded-xl shadow-lg p-8 text-center max-w-2xl">
            <MessageSquare size={64} className="text-gray-400 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 mb-4">没有进行中的讨论</h2>
            <p className="text-gray-600">请先创建一个新的讨论来开始智能体对话。</p>
          </div>
        </div>
      </div>
    );
  }
  const participants = state.agents.filter(agent => 
    currentDiscussion.participants.includes(agent.id)
  );
  return (
    <div className="h-screen bg-gradient-to-br from-blue-50 to-indigo-50 w-full overflow-hidden pt-16">
      <div className="discussion-container h-full">
        <div className="flex gap-6 h-full max-w-none w-full">
          {/* 主持人卡片 - 最左边一列 */}
          {currentDiscussion.moderatorId && (() => {
            const moderator = state.agents.find(agent => agent.id === currentDiscussion.moderatorId);
            return moderator ? (
              <div className="fixed-size-sidebar flex-shrink-0">
                <div className="bg-white rounded-xl shadow-lg p-6 h-fit">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                      <Users size={16} className="text-purple-600" />
                    </div>
                    <h3 className="font-semibold text-gray-900">主持人</h3>
                  </div>

                  <div className="space-y-4">
                    {/* 主持人基本信息 */}
                    <div className="flex items-center gap-3">
                      <img
                        src={moderator.avatar}
                        alt={moderator.name}
                        className="w-12 h-12 rounded-full object-cover"
                      />
                      <div>
                        <h4 className="font-medium text-gray-900">{moderator.name}</h4>
                        <p className="text-sm text-gray-500">
                          {moderator.expertise.slice(0, 2).join('、')}
                        </p>
                      </div>
                    </div>

                    {/* 主持人配置信息 */}
                    {moderator.moderatorConfig && (
                      <div className="bg-purple-50 border border-purple-200 rounded-lg p-3">
                        <h5 className="text-sm font-medium text-purple-900 mb-2">管理风格</h5>
                        <p className="text-sm text-purple-700">
                          {moderator.moderatorConfig.managementStyle === 'strict' ? '严格型' :
                           moderator.moderatorConfig.managementStyle === 'flexible' ? '灵活型' : '协作型'}
                        </p>
                      </div>
                    )}

                    {/* 话题相关性 */}
                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-600">话题相关性</span>
                        <span className={`font-medium ${
                          topicRelevance > 0.8 ? 'text-green-600' :
                          topicRelevance > 0.6 ? 'text-yellow-600' : 'text-red-600'
                        }`}>
                          {Math.round(topicRelevance * 100)}%
                        </span>
                      </div>
                      <div className="w-full h-2 bg-gray-200 rounded-full overflow-hidden">
                        <div
                          className={`h-full transition-all duration-500 ${
                            topicRelevance > 0.8 ? 'bg-green-500' :
                            topicRelevance > 0.6 ? 'bg-yellow-500' : 'bg-red-500'
                          }`}
                          style={{ width: `${topicRelevance * 100}%` }}
                        />
                      </div>
                    </div>

                    {/* 实时总结 */}
                    {moderatorSummary && (
                      <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                        <h5 className="text-sm font-medium text-blue-900 mb-2">最新总结</h5>
                        <p className="text-sm text-blue-700">{moderatorSummary}</p>
                      </div>
                    )}

                    {/* 干预统计 */}
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600">干预次数</span>
                      <span className="font-medium text-gray-900">
                        {currentDiscussion.moderatorInterventions || 0}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            ) : null;
          })()}

          {/* 主讨论区 */}
          <div className="fixed-size-discussion flex-shrink-0 h-[70vh]">
            <div className="bg-white rounded-xl shadow-lg overflow-hidden h-full flex flex-col">
              
              {/* 讨论头部 */}
              <div className={`text-white p-6 ${state.isDiscussionActive ? 'bg-gradient-to-r from-blue-600 to-indigo-600' : 'bg-gradient-to-r from-gray-600 to-gray-700'}`}>
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <MessageSquare size={32} />
                    <div>
                      <h1 className="text-2xl font-bold">
                        {state.isDiscussionActive ? '讨论进行中' : '历史讨论记录'}
                      </h1>
                      <p className={state.isDiscussionActive ? 'text-blue-100' : 'text-gray-100'}>
                        {currentDiscussion.mode === 'free' ? '自由讨论模式' : '主持人模式'}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center gap-4">
                    {state.isDiscussionActive ? (
                      <>
                        <div className="text-center">
                          <div className="text-2xl font-bold">{formatTime(discussionStats.activeTime)}</div>
                          <div className="text-sm text-blue-100">讨论时长</div>
                        </div>

                        <button
                          onClick={handleEndDiscussion}
                          className="flex items-center gap-2 bg-red-500 hover:bg-red-600 px-4 py-2 rounded-lg transition-colors"
                        >
                          <StopCircle size={20} />
                          结束讨论
                        </button>
                      </>
                    ) : (
                      <div className="text-center">
                        <div className="text-sm text-gray-100">创建时间</div>
                        <div className="text-lg font-bold">
                          {new Date(currentDiscussion.createdAt).toLocaleString()}
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                <div className="bg-white bg-opacity-20 rounded-lg p-4">
                  <h3 className="font-semibold mb-2">讨论话题</h3>
                  <p className={state.isDiscussionActive ? 'text-blue-50' : 'text-gray-50'}>
                    {currentDiscussion.topic}
                  </p>
                </div>
              </div>
              {/* 消息列表 */}
              <div className="h-[calc(100%-180px)] overflow-y-auto p-6 space-y-4">
                {currentDiscussion.messages.map((message) => (
                  <MessageBubble
                    key={message.id}
                    message={message}
                    agent={participants.find(a => a.id === message.agentId)!}
                  />
                ))}

                {isSimulating && state.isDiscussionActive && (
                  <div className="flex items-center gap-3 text-gray-500">
                    <div className="animate-spin w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full"></div>
                    <span>智能体正在思考中...</span>
                  </div>
                )}
                
                <div ref={messagesEndRef} />
              </div>
            </div>
          </div>
          {/* 侧边栏 */}
          <div className="space-y-6 fixed-size-sidebar">



            {/* 共识度指示器 */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <div className="flex items-center gap-3 mb-4">
                <TrendingUp size={24} className="text-green-600" />
                <h3 className="font-semibold text-gray-900">共识度</h3>
              </div>
              
              <div className="space-y-4">
                <div className="relative">
                  <div className="w-full h-4 bg-gray-200 rounded-full overflow-hidden">
                    <div 
                      className={`h-full transition-all duration-500 ${
                        discussionStats.consensusScore > 80 
                          ? 'bg-green-500' 
                          : discussionStats.consensusScore > 60 
                            ? 'bg-yellow-500' 
                            : 'bg-red-500'
                      }`}
                      style={{ width: `${discussionStats.consensusScore}%` }}
                    />
                  </div>
                  <div className="text-center mt-2 font-bold text-2xl">
                    {Math.round(discussionStats.consensusScore)}%
                  </div>
                </div>
                
                {currentDiscussion.status === 'consensus' && (
                  <div className="flex items-center gap-2 bg-green-50 text-green-800 p-3 rounded-lg">
                    <CheckCircle size={20} />
                    <span className="font-medium">已达成共识！</span>
                  </div>
                )}
              </div>
            </div>
            {/* 参与者列表 */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <div className="flex items-center gap-3 mb-4">
                <Users size={24} className="text-blue-600" />
                <h3 className="font-semibold text-gray-900">参与者</h3>
              </div>
              
              <div className="space-y-3">
                {participants
                  .filter(agent => agent.id !== currentDiscussion.moderatorId) // 排除主持人
                  .map((agent) => {
                    const messageCount = currentDiscussion.messages.filter(m => m.agentId === agent.id).length;
                    const lastMessage = currentDiscussion.messages
                      .slice()
                      .reverse()
                      .find(m => m.agentId === agent.id);

                    return (
                      <div key={agent.id} className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                        <img
                          src={agent.avatar}
                          alt={agent.name}
                          className="w-10 h-10 rounded-full object-cover"
                        />
                        <div className="flex-1">
                          <div className="font-medium text-gray-900">{agent.name}</div>
                          <div className="text-sm text-gray-500">
                            {messageCount} 条消息
                          </div>
                        </div>

                        {lastMessage && (
                          <div className="text-xs text-gray-400">
                            {new Date(lastMessage.timestamp).toLocaleTimeString()}
                          </div>
                        )}
                      </div>
                    );
                  })}
              </div>
            </div>
            {/* 讨论统计 */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <div className="flex items-center gap-3 mb-4">
                <BarChart3 size={24} className="text-purple-600" />
                <h3 className="font-semibold text-gray-900">讨论统计</h3>
              </div>
              
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">总消息数</span>
                  <span className="font-bold text-lg">{discussionStats.totalMessages}</span>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">讨论时长</span>
                  <span className="font-bold text-lg">{formatTime(discussionStats.activeTime)}</span>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">参与者数量</span>
                  <span className="font-bold text-lg">{participants.length}</span>
                </div>
              </div>
            </div>
            {/* 共识结论 */}
            {currentDiscussion.consensus && (
              <div className="bg-green-50 border border-green-200 rounded-xl p-6">
                <div className="flex items-center gap-3 mb-3">
                  <CheckCircle size={24} className="text-green-600" />
                  <h3 className="font-semibold text-green-900">讨论结论</h3>
                </div>
                <p className="text-green-800">{currentDiscussion.consensus}</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
function MessageBubble({ message, agent }: { message: Message; agent: any }) {
  const getMessageIcon = () => {
    switch (message.type) {
      case 'question':
        return <HelpCircle size={16} className="text-blue-500" />;
      case 'agreement':
        return <ThumbsUp size={16} className="text-green-500" />;
      case 'disagreement':
        return <ThumbsDown size={16} className="text-red-500" />;
      default:
        return <MessageSquare size={16} className="text-gray-500" />;
    }
  };
  const getMessageBorderColor = () => {
    switch (message.type) {
      case 'question':
        return 'border-l-blue-500';
      case 'agreement':
        return 'border-l-green-500';
      case 'disagreement':
        return 'border-l-red-500';
      default:
        return 'border-l-gray-300';
    }
  };
  return (
    <div className={`flex gap-4 p-4 bg-gray-50 rounded-lg border-l-4 ${getMessageBorderColor()}`}>
      <img
        src={agent?.avatar}
        alt={agent?.name}
        className="w-12 h-12 rounded-full object-cover flex-shrink-0"
      />
      
      <div className="flex-1">
        <div className="flex items-center gap-2 mb-2">
          <span className="font-semibold text-gray-900">{agent?.name}</span>
          {getMessageIcon()}
          <span className="text-xs text-gray-500">
            {new Date(message.timestamp).toLocaleTimeString()}
          </span>
        </div>
        
        <p className="text-gray-800 leading-relaxed">{message.content}</p>
      </div>
    </div>
  );
}
