var ud=Object.defineProperty;var dd=(e,t,n)=>t in e?ud(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var ge=(e,t,n)=>(dd(e,typeof t!="symbol"?t+"":t,n),n);(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const l of document.querySelectorAll('link[rel="modulepreload"]'))r(l);new MutationObserver(l=>{for(const i of l)if(i.type==="childList")for(const o of i.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&r(o)}).observe(document,{childList:!0,subtree:!0});function n(l){const i={};return l.integrity&&(i.integrity=l.integrity),l.referrerPolicy&&(i.referrerPolicy=l.referrerPolicy),l.crossOrigin==="use-credentials"?i.credentials="include":l.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(l){if(l.ep)return;l.ep=!0;const i=n(l);fetch(l.href,i)}})();function md(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Ua={exports:{}},_s={},Fa={exports:{}},R={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var wr=Symbol.for("react.element"),fd=Symbol.for("react.portal"),pd=Symbol.for("react.fragment"),hd=Symbol.for("react.strict_mode"),gd=Symbol.for("react.profiler"),xd=Symbol.for("react.provider"),yd=Symbol.for("react.context"),vd=Symbol.for("react.forward_ref"),jd=Symbol.for("react.suspense"),wd=Symbol.for("react.memo"),Nd=Symbol.for("react.lazy"),So=Symbol.iterator;function Sd(e){return e===null||typeof e!="object"?null:(e=So&&e[So]||e["@@iterator"],typeof e=="function"?e:null)}var Va={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Ha=Object.assign,Ba={};function In(e,t,n){this.props=e,this.context=t,this.refs=Ba,this.updater=n||Va}In.prototype.isReactComponent={};In.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};In.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Wa(){}Wa.prototype=In.prototype;function ki(e,t,n){this.props=e,this.context=t,this.refs=Ba,this.updater=n||Va}var bi=ki.prototype=new Wa;bi.constructor=ki;Ha(bi,In.prototype);bi.isPureReactComponent=!0;var ko=Array.isArray,Qa=Object.prototype.hasOwnProperty,Ci={current:null},Ka={key:!0,ref:!0,__self:!0,__source:!0};function Ga(e,t,n){var r,l={},i=null,o=null;if(t!=null)for(r in t.ref!==void 0&&(o=t.ref),t.key!==void 0&&(i=""+t.key),t)Qa.call(t,r)&&!Ka.hasOwnProperty(r)&&(l[r]=t[r]);var a=arguments.length-2;if(a===1)l.children=n;else if(1<a){for(var c=Array(a),f=0;f<a;f++)c[f]=arguments[f+2];l.children=c}if(e&&e.defaultProps)for(r in a=e.defaultProps,a)l[r]===void 0&&(l[r]=a[r]);return{$$typeof:wr,type:e,key:i,ref:o,props:l,_owner:Ci.current}}function kd(e,t){return{$$typeof:wr,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Ei(e){return typeof e=="object"&&e!==null&&e.$$typeof===wr}function bd(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var bo=/\/+/g;function Xs(e,t){return typeof e=="object"&&e!==null&&e.key!=null?bd(""+e.key):t.toString(36)}function Qr(e,t,n,r,l){var i=typeof e;(i==="undefined"||i==="boolean")&&(e=null);var o=!1;if(e===null)o=!0;else switch(i){case"string":case"number":o=!0;break;case"object":switch(e.$$typeof){case wr:case fd:o=!0}}if(o)return o=e,l=l(o),e=r===""?"."+Xs(o,0):r,ko(l)?(n="",e!=null&&(n=e.replace(bo,"$&/")+"/"),Qr(l,t,n,"",function(f){return f})):l!=null&&(Ei(l)&&(l=kd(l,n+(!l.key||o&&o.key===l.key?"":(""+l.key).replace(bo,"$&/")+"/")+e)),t.push(l)),1;if(o=0,r=r===""?".":r+":",ko(e))for(var a=0;a<e.length;a++){i=e[a];var c=r+Xs(i,a);o+=Qr(i,t,n,c,l)}else if(c=Sd(e),typeof c=="function")for(e=c.call(e),a=0;!(i=e.next()).done;)i=i.value,c=r+Xs(i,a++),o+=Qr(i,t,n,c,l);else if(i==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return o}function Lr(e,t,n){if(e==null)return e;var r=[],l=0;return Qr(e,r,"","",function(i){return t.call(n,i,l++)}),r}function Cd(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var ve={current:null},Kr={transition:null},Ed={ReactCurrentDispatcher:ve,ReactCurrentBatchConfig:Kr,ReactCurrentOwner:Ci};function qa(){throw Error("act(...) is not supported in production builds of React.")}R.Children={map:Lr,forEach:function(e,t,n){Lr(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return Lr(e,function(){t++}),t},toArray:function(e){return Lr(e,function(t){return t})||[]},only:function(e){if(!Ei(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};R.Component=In;R.Fragment=pd;R.Profiler=gd;R.PureComponent=ki;R.StrictMode=hd;R.Suspense=jd;R.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Ed;R.act=qa;R.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=Ha({},e.props),l=e.key,i=e.ref,o=e._owner;if(t!=null){if(t.ref!==void 0&&(i=t.ref,o=Ci.current),t.key!==void 0&&(l=""+t.key),e.type&&e.type.defaultProps)var a=e.type.defaultProps;for(c in t)Qa.call(t,c)&&!Ka.hasOwnProperty(c)&&(r[c]=t[c]===void 0&&a!==void 0?a[c]:t[c])}var c=arguments.length-2;if(c===1)r.children=n;else if(1<c){a=Array(c);for(var f=0;f<c;f++)a[f]=arguments[f+2];r.children=a}return{$$typeof:wr,type:e.type,key:l,ref:i,props:r,_owner:o}};R.createContext=function(e){return e={$$typeof:yd,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:xd,_context:e},e.Consumer=e};R.createElement=Ga;R.createFactory=function(e){var t=Ga.bind(null,e);return t.type=e,t};R.createRef=function(){return{current:null}};R.forwardRef=function(e){return{$$typeof:vd,render:e}};R.isValidElement=Ei;R.lazy=function(e){return{$$typeof:Nd,_payload:{_status:-1,_result:e},_init:Cd}};R.memo=function(e,t){return{$$typeof:wd,type:e,compare:t===void 0?null:t}};R.startTransition=function(e){var t=Kr.transition;Kr.transition={};try{e()}finally{Kr.transition=t}};R.unstable_act=qa;R.useCallback=function(e,t){return ve.current.useCallback(e,t)};R.useContext=function(e){return ve.current.useContext(e)};R.useDebugValue=function(){};R.useDeferredValue=function(e){return ve.current.useDeferredValue(e)};R.useEffect=function(e,t){return ve.current.useEffect(e,t)};R.useId=function(){return ve.current.useId()};R.useImperativeHandle=function(e,t,n){return ve.current.useImperativeHandle(e,t,n)};R.useInsertionEffect=function(e,t){return ve.current.useInsertionEffect(e,t)};R.useLayoutEffect=function(e,t){return ve.current.useLayoutEffect(e,t)};R.useMemo=function(e,t){return ve.current.useMemo(e,t)};R.useReducer=function(e,t,n){return ve.current.useReducer(e,t,n)};R.useRef=function(e){return ve.current.useRef(e)};R.useState=function(e){return ve.current.useState(e)};R.useSyncExternalStore=function(e,t,n){return ve.current.useSyncExternalStore(e,t,n)};R.useTransition=function(){return ve.current.useTransition()};R.version="18.3.1";Fa.exports=R;var D=Fa.exports;const Ya=md(D);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ld=D,Dd=Symbol.for("react.element"),Md=Symbol.for("react.fragment"),Td=Object.prototype.hasOwnProperty,Ad=Ld.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Id={key:!0,ref:!0,__self:!0,__source:!0};function Xa(e,t,n){var r,l={},i=null,o=null;n!==void 0&&(i=""+n),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(o=t.ref);for(r in t)Td.call(t,r)&&!Id.hasOwnProperty(r)&&(l[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)l[r]===void 0&&(l[r]=t[r]);return{$$typeof:Dd,type:e,key:i,ref:o,props:l,_owner:Ad.current}}_s.Fragment=Md;_s.jsx=Xa;_s.jsxs=Xa;Ua.exports=_s;var s=Ua.exports,bl={},Za={exports:{}},Ae={},Ja={exports:{}},ec={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(E,k){var C=E.length;E.push(k);e:for(;0<C;){var A=C-1>>>1,z=E[A];if(0<l(z,k))E[A]=k,E[C]=z,C=A;else break e}}function n(E){return E.length===0?null:E[0]}function r(E){if(E.length===0)return null;var k=E[0],C=E.pop();if(C!==k){E[0]=C;e:for(var A=0,z=E.length,K=z>>>1;A<K;){var I=2*(A+1)-1,ie=E[I],H=I+1,$t=E[H];if(0>l(ie,C))H<z&&0>l($t,ie)?(E[A]=$t,E[H]=C,A=H):(E[A]=ie,E[I]=C,A=I);else if(H<z&&0>l($t,C))E[A]=$t,E[H]=C,A=H;else break e}}return k}function l(E,k){var C=E.sortIndex-k.sortIndex;return C!==0?C:E.id-k.id}if(typeof performance=="object"&&typeof performance.now=="function"){var i=performance;e.unstable_now=function(){return i.now()}}else{var o=Date,a=o.now();e.unstable_now=function(){return o.now()-a}}var c=[],f=[],x=1,g=null,y=3,v=!1,N=!1,h=!1,S=typeof setTimeout=="function"?setTimeout:null,m=typeof clearTimeout=="function"?clearTimeout:null,d=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function u(E){for(var k=n(f);k!==null;){if(k.callback===null)r(f);else if(k.startTime<=E)r(f),k.sortIndex=k.expirationTime,t(c,k);else break;k=n(f)}}function p(E){if(h=!1,u(E),!N)if(n(c)!==null)N=!0,Rt(j);else{var k=n(f);k!==null&&rn(p,k.startTime-E)}}function j(E,k){N=!1,h&&(h=!1,m(T),T=-1),v=!0;var C=y;try{for(u(k),g=n(c);g!==null&&(!(g.expirationTime>k)||E&&!we());){var A=g.callback;if(typeof A=="function"){g.callback=null,y=g.priorityLevel;var z=A(g.expirationTime<=k);k=e.unstable_now(),typeof z=="function"?g.callback=z:g===n(c)&&r(c),u(k)}else r(c);g=n(c)}if(g!==null)var K=!0;else{var I=n(f);I!==null&&rn(p,I.startTime-k),K=!1}return K}finally{g=null,y=C,v=!1}}var w=!1,M=null,T=-1,O=5,_=-1;function we(){return!(e.unstable_now()-_<O)}function _t(){if(M!==null){var E=e.unstable_now();_=E;var k=!0;try{k=M(!0,E)}finally{k?zt():(w=!1,M=null)}}else w=!1}var zt;if(typeof d=="function")zt=function(){d(_t)};else if(typeof MessageChannel<"u"){var Cr=new MessageChannel,Er=Cr.port2;Cr.port1.onmessage=_t,zt=function(){Er.postMessage(null)}}else zt=function(){S(_t,0)};function Rt(E){M=E,w||(w=!0,zt())}function rn(E,k){T=S(function(){E(e.unstable_now())},k)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(E){E.callback=null},e.unstable_continueExecution=function(){N||v||(N=!0,Rt(j))},e.unstable_forceFrameRate=function(E){0>E||125<E?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):O=0<E?Math.floor(1e3/E):5},e.unstable_getCurrentPriorityLevel=function(){return y},e.unstable_getFirstCallbackNode=function(){return n(c)},e.unstable_next=function(E){switch(y){case 1:case 2:case 3:var k=3;break;default:k=y}var C=y;y=k;try{return E()}finally{y=C}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(E,k){switch(E){case 1:case 2:case 3:case 4:case 5:break;default:E=3}var C=y;y=E;try{return k()}finally{y=C}},e.unstable_scheduleCallback=function(E,k,C){var A=e.unstable_now();switch(typeof C=="object"&&C!==null?(C=C.delay,C=typeof C=="number"&&0<C?A+C:A):C=A,E){case 1:var z=-1;break;case 2:z=250;break;case 5:z=**********;break;case 4:z=1e4;break;default:z=5e3}return z=C+z,E={id:x++,callback:k,priorityLevel:E,startTime:C,expirationTime:z,sortIndex:-1},C>A?(E.sortIndex=C,t(f,E),n(c)===null&&E===n(f)&&(h?(m(T),T=-1):h=!0,rn(p,C-A))):(E.sortIndex=z,t(c,E),N||v||(N=!0,Rt(j))),E},e.unstable_shouldYield=we,e.unstable_wrapCallback=function(E){var k=y;return function(){var C=y;y=k;try{return E.apply(this,arguments)}finally{y=C}}}})(ec);Ja.exports=ec;var Pd=Ja.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var _d=D,Te=Pd;function b(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var tc=new Set,rr={};function tn(e,t){bn(e,t),bn(e+"Capture",t)}function bn(e,t){for(rr[e]=t,e=0;e<t.length;e++)tc.add(t[e])}var it=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Cl=Object.prototype.hasOwnProperty,zd=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Co={},Eo={};function Rd(e){return Cl.call(Eo,e)?!0:Cl.call(Co,e)?!1:zd.test(e)?Eo[e]=!0:(Co[e]=!0,!1)}function $d(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function Od(e,t,n,r){if(t===null||typeof t>"u"||$d(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function je(e,t,n,r,l,i,o){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=l,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=o}var de={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){de[e]=new je(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];de[t]=new je(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){de[e]=new je(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){de[e]=new je(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){de[e]=new je(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){de[e]=new je(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){de[e]=new je(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){de[e]=new je(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){de[e]=new je(e,5,!1,e.toLowerCase(),null,!1,!1)});var Li=/[\-:]([a-z])/g;function Di(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Li,Di);de[t]=new je(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Li,Di);de[t]=new je(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Li,Di);de[t]=new je(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){de[e]=new je(e,1,!1,e.toLowerCase(),null,!1,!1)});de.xlinkHref=new je("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){de[e]=new je(e,1,!1,e.toLowerCase(),null,!0,!0)});function Mi(e,t,n,r){var l=de.hasOwnProperty(t)?de[t]:null;(l!==null?l.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(Od(t,n,l,r)&&(n=null),r||l===null?Rd(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):l.mustUseProperty?e[l.propertyName]=n===null?l.type===3?!1:"":n:(t=l.attributeName,r=l.attributeNamespace,n===null?e.removeAttribute(t):(l=l.type,n=l===3||l===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var ut=_d.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Dr=Symbol.for("react.element"),on=Symbol.for("react.portal"),an=Symbol.for("react.fragment"),Ti=Symbol.for("react.strict_mode"),El=Symbol.for("react.profiler"),nc=Symbol.for("react.provider"),rc=Symbol.for("react.context"),Ai=Symbol.for("react.forward_ref"),Ll=Symbol.for("react.suspense"),Dl=Symbol.for("react.suspense_list"),Ii=Symbol.for("react.memo"),ft=Symbol.for("react.lazy"),sc=Symbol.for("react.offscreen"),Lo=Symbol.iterator;function zn(e){return e===null||typeof e!="object"?null:(e=Lo&&e[Lo]||e["@@iterator"],typeof e=="function"?e:null)}var X=Object.assign,Zs;function Bn(e){if(Zs===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Zs=t&&t[1]||""}return`
`+Zs+e}var Js=!1;function el(e,t){if(!e||Js)return"";Js=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(f){var r=f}Reflect.construct(e,[],t)}else{try{t.call()}catch(f){r=f}e.call(t.prototype)}else{try{throw Error()}catch(f){r=f}e()}}catch(f){if(f&&r&&typeof f.stack=="string"){for(var l=f.stack.split(`
`),i=r.stack.split(`
`),o=l.length-1,a=i.length-1;1<=o&&0<=a&&l[o]!==i[a];)a--;for(;1<=o&&0<=a;o--,a--)if(l[o]!==i[a]){if(o!==1||a!==1)do if(o--,a--,0>a||l[o]!==i[a]){var c=`
`+l[o].replace(" at new "," at ");return e.displayName&&c.includes("<anonymous>")&&(c=c.replace("<anonymous>",e.displayName)),c}while(1<=o&&0<=a);break}}}finally{Js=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Bn(e):""}function Ud(e){switch(e.tag){case 5:return Bn(e.type);case 16:return Bn("Lazy");case 13:return Bn("Suspense");case 19:return Bn("SuspenseList");case 0:case 2:case 15:return e=el(e.type,!1),e;case 11:return e=el(e.type.render,!1),e;case 1:return e=el(e.type,!0),e;default:return""}}function Ml(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case an:return"Fragment";case on:return"Portal";case El:return"Profiler";case Ti:return"StrictMode";case Ll:return"Suspense";case Dl:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case rc:return(e.displayName||"Context")+".Consumer";case nc:return(e._context.displayName||"Context")+".Provider";case Ai:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Ii:return t=e.displayName||null,t!==null?t:Ml(e.type)||"Memo";case ft:t=e._payload,e=e._init;try{return Ml(e(t))}catch{}}return null}function Fd(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Ml(t);case 8:return t===Ti?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Dt(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function lc(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Vd(e){var t=lc(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var l=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return l.call(this)},set:function(o){r=""+o,i.call(this,o)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(o){r=""+o},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Mr(e){e._valueTracker||(e._valueTracker=Vd(e))}function ic(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=lc(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function ls(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Tl(e,t){var n=t.checked;return X({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Do(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=Dt(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function oc(e,t){t=t.checked,t!=null&&Mi(e,"checked",t,!1)}function Al(e,t){oc(e,t);var n=Dt(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Il(e,t.type,n):t.hasOwnProperty("defaultValue")&&Il(e,t.type,Dt(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Mo(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Il(e,t,n){(t!=="number"||ls(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Wn=Array.isArray;function vn(e,t,n,r){if(e=e.options,t){t={};for(var l=0;l<n.length;l++)t["$"+n[l]]=!0;for(n=0;n<e.length;n++)l=t.hasOwnProperty("$"+e[n].value),e[n].selected!==l&&(e[n].selected=l),l&&r&&(e[n].defaultSelected=!0)}else{for(n=""+Dt(n),t=null,l=0;l<e.length;l++){if(e[l].value===n){e[l].selected=!0,r&&(e[l].defaultSelected=!0);return}t!==null||e[l].disabled||(t=e[l])}t!==null&&(t.selected=!0)}}function Pl(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(b(91));return X({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function To(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(b(92));if(Wn(n)){if(1<n.length)throw Error(b(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:Dt(n)}}function ac(e,t){var n=Dt(t.value),r=Dt(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Ao(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function cc(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function _l(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?cc(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Tr,uc=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,l){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,l)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Tr=Tr||document.createElement("div"),Tr.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Tr.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function sr(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Gn={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Hd=["Webkit","ms","Moz","O"];Object.keys(Gn).forEach(function(e){Hd.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Gn[t]=Gn[e]})});function dc(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Gn.hasOwnProperty(e)&&Gn[e]?(""+t).trim():t+"px"}function mc(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,l=dc(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,l):e[n]=l}}var Bd=X({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function zl(e,t){if(t){if(Bd[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(b(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(b(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(b(61))}if(t.style!=null&&typeof t.style!="object")throw Error(b(62))}}function Rl(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var $l=null;function Pi(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Ol=null,jn=null,wn=null;function Io(e){if(e=kr(e)){if(typeof Ol!="function")throw Error(b(280));var t=e.stateNode;t&&(t=Us(t),Ol(e.stateNode,e.type,t))}}function fc(e){jn?wn?wn.push(e):wn=[e]:jn=e}function pc(){if(jn){var e=jn,t=wn;if(wn=jn=null,Io(e),t)for(e=0;e<t.length;e++)Io(t[e])}}function hc(e,t){return e(t)}function gc(){}var tl=!1;function xc(e,t,n){if(tl)return e(t,n);tl=!0;try{return hc(e,t,n)}finally{tl=!1,(jn!==null||wn!==null)&&(gc(),pc())}}function lr(e,t){var n=e.stateNode;if(n===null)return null;var r=Us(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(b(231,t,typeof n));return n}var Ul=!1;if(it)try{var Rn={};Object.defineProperty(Rn,"passive",{get:function(){Ul=!0}}),window.addEventListener("test",Rn,Rn),window.removeEventListener("test",Rn,Rn)}catch{Ul=!1}function Wd(e,t,n,r,l,i,o,a,c){var f=Array.prototype.slice.call(arguments,3);try{t.apply(n,f)}catch(x){this.onError(x)}}var qn=!1,is=null,os=!1,Fl=null,Qd={onError:function(e){qn=!0,is=e}};function Kd(e,t,n,r,l,i,o,a,c){qn=!1,is=null,Wd.apply(Qd,arguments)}function Gd(e,t,n,r,l,i,o,a,c){if(Kd.apply(this,arguments),qn){if(qn){var f=is;qn=!1,is=null}else throw Error(b(198));os||(os=!0,Fl=f)}}function nn(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function yc(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Po(e){if(nn(e)!==e)throw Error(b(188))}function qd(e){var t=e.alternate;if(!t){if(t=nn(e),t===null)throw Error(b(188));return t!==e?null:e}for(var n=e,r=t;;){var l=n.return;if(l===null)break;var i=l.alternate;if(i===null){if(r=l.return,r!==null){n=r;continue}break}if(l.child===i.child){for(i=l.child;i;){if(i===n)return Po(l),e;if(i===r)return Po(l),t;i=i.sibling}throw Error(b(188))}if(n.return!==r.return)n=l,r=i;else{for(var o=!1,a=l.child;a;){if(a===n){o=!0,n=l,r=i;break}if(a===r){o=!0,r=l,n=i;break}a=a.sibling}if(!o){for(a=i.child;a;){if(a===n){o=!0,n=i,r=l;break}if(a===r){o=!0,r=i,n=l;break}a=a.sibling}if(!o)throw Error(b(189))}}if(n.alternate!==r)throw Error(b(190))}if(n.tag!==3)throw Error(b(188));return n.stateNode.current===n?e:t}function vc(e){return e=qd(e),e!==null?jc(e):null}function jc(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=jc(e);if(t!==null)return t;e=e.sibling}return null}var wc=Te.unstable_scheduleCallback,_o=Te.unstable_cancelCallback,Yd=Te.unstable_shouldYield,Xd=Te.unstable_requestPaint,J=Te.unstable_now,Zd=Te.unstable_getCurrentPriorityLevel,_i=Te.unstable_ImmediatePriority,Nc=Te.unstable_UserBlockingPriority,as=Te.unstable_NormalPriority,Jd=Te.unstable_LowPriority,Sc=Te.unstable_IdlePriority,zs=null,Xe=null;function em(e){if(Xe&&typeof Xe.onCommitFiberRoot=="function")try{Xe.onCommitFiberRoot(zs,e,void 0,(e.current.flags&128)===128)}catch{}}var We=Math.clz32?Math.clz32:rm,tm=Math.log,nm=Math.LN2;function rm(e){return e>>>=0,e===0?32:31-(tm(e)/nm|0)|0}var Ar=64,Ir=4194304;function Qn(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function cs(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,l=e.suspendedLanes,i=e.pingedLanes,o=n&268435455;if(o!==0){var a=o&~l;a!==0?r=Qn(a):(i&=o,i!==0&&(r=Qn(i)))}else o=n&~l,o!==0?r=Qn(o):i!==0&&(r=Qn(i));if(r===0)return 0;if(t!==0&&t!==r&&!(t&l)&&(l=r&-r,i=t&-t,l>=i||l===16&&(i&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-We(t),l=1<<n,r|=e[n],t&=~l;return r}function sm(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function lm(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,l=e.expirationTimes,i=e.pendingLanes;0<i;){var o=31-We(i),a=1<<o,c=l[o];c===-1?(!(a&n)||a&r)&&(l[o]=sm(a,t)):c<=t&&(e.expiredLanes|=a),i&=~a}}function Vl(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function kc(){var e=Ar;return Ar<<=1,!(Ar&4194240)&&(Ar=64),e}function nl(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Nr(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-We(t),e[t]=n}function im(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var l=31-We(n),i=1<<l;t[l]=0,r[l]=-1,e[l]=-1,n&=~i}}function zi(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-We(n),l=1<<r;l&t|e[r]&t&&(e[r]|=t),n&=~l}}var F=0;function bc(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Cc,Ri,Ec,Lc,Dc,Hl=!1,Pr=[],vt=null,jt=null,wt=null,ir=new Map,or=new Map,ht=[],om="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function zo(e,t){switch(e){case"focusin":case"focusout":vt=null;break;case"dragenter":case"dragleave":jt=null;break;case"mouseover":case"mouseout":wt=null;break;case"pointerover":case"pointerout":ir.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":or.delete(t.pointerId)}}function $n(e,t,n,r,l,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[l]},t!==null&&(t=kr(t),t!==null&&Ri(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,l!==null&&t.indexOf(l)===-1&&t.push(l),e)}function am(e,t,n,r,l){switch(t){case"focusin":return vt=$n(vt,e,t,n,r,l),!0;case"dragenter":return jt=$n(jt,e,t,n,r,l),!0;case"mouseover":return wt=$n(wt,e,t,n,r,l),!0;case"pointerover":var i=l.pointerId;return ir.set(i,$n(ir.get(i)||null,e,t,n,r,l)),!0;case"gotpointercapture":return i=l.pointerId,or.set(i,$n(or.get(i)||null,e,t,n,r,l)),!0}return!1}function Mc(e){var t=Wt(e.target);if(t!==null){var n=nn(t);if(n!==null){if(t=n.tag,t===13){if(t=yc(n),t!==null){e.blockedOn=t,Dc(e.priority,function(){Ec(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Gr(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Bl(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);$l=r,n.target.dispatchEvent(r),$l=null}else return t=kr(n),t!==null&&Ri(t),e.blockedOn=n,!1;t.shift()}return!0}function Ro(e,t,n){Gr(e)&&n.delete(t)}function cm(){Hl=!1,vt!==null&&Gr(vt)&&(vt=null),jt!==null&&Gr(jt)&&(jt=null),wt!==null&&Gr(wt)&&(wt=null),ir.forEach(Ro),or.forEach(Ro)}function On(e,t){e.blockedOn===t&&(e.blockedOn=null,Hl||(Hl=!0,Te.unstable_scheduleCallback(Te.unstable_NormalPriority,cm)))}function ar(e){function t(l){return On(l,e)}if(0<Pr.length){On(Pr[0],e);for(var n=1;n<Pr.length;n++){var r=Pr[n];r.blockedOn===e&&(r.blockedOn=null)}}for(vt!==null&&On(vt,e),jt!==null&&On(jt,e),wt!==null&&On(wt,e),ir.forEach(t),or.forEach(t),n=0;n<ht.length;n++)r=ht[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<ht.length&&(n=ht[0],n.blockedOn===null);)Mc(n),n.blockedOn===null&&ht.shift()}var Nn=ut.ReactCurrentBatchConfig,us=!0;function um(e,t,n,r){var l=F,i=Nn.transition;Nn.transition=null;try{F=1,$i(e,t,n,r)}finally{F=l,Nn.transition=i}}function dm(e,t,n,r){var l=F,i=Nn.transition;Nn.transition=null;try{F=4,$i(e,t,n,r)}finally{F=l,Nn.transition=i}}function $i(e,t,n,r){if(us){var l=Bl(e,t,n,r);if(l===null)ml(e,t,r,ds,n),zo(e,r);else if(am(l,e,t,n,r))r.stopPropagation();else if(zo(e,r),t&4&&-1<om.indexOf(e)){for(;l!==null;){var i=kr(l);if(i!==null&&Cc(i),i=Bl(e,t,n,r),i===null&&ml(e,t,r,ds,n),i===l)break;l=i}l!==null&&r.stopPropagation()}else ml(e,t,r,null,n)}}var ds=null;function Bl(e,t,n,r){if(ds=null,e=Pi(r),e=Wt(e),e!==null)if(t=nn(e),t===null)e=null;else if(n=t.tag,n===13){if(e=yc(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return ds=e,null}function Tc(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Zd()){case _i:return 1;case Nc:return 4;case as:case Jd:return 16;case Sc:return 536870912;default:return 16}default:return 16}}var xt=null,Oi=null,qr=null;function Ac(){if(qr)return qr;var e,t=Oi,n=t.length,r,l="value"in xt?xt.value:xt.textContent,i=l.length;for(e=0;e<n&&t[e]===l[e];e++);var o=n-e;for(r=1;r<=o&&t[n-r]===l[i-r];r++);return qr=l.slice(e,1<r?1-r:void 0)}function Yr(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function _r(){return!0}function $o(){return!1}function Ie(e){function t(n,r,l,i,o){this._reactName=n,this._targetInst=l,this.type=r,this.nativeEvent=i,this.target=o,this.currentTarget=null;for(var a in e)e.hasOwnProperty(a)&&(n=e[a],this[a]=n?n(i):i[a]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?_r:$o,this.isPropagationStopped=$o,this}return X(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=_r)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=_r)},persist:function(){},isPersistent:_r}),t}var Pn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Ui=Ie(Pn),Sr=X({},Pn,{view:0,detail:0}),mm=Ie(Sr),rl,sl,Un,Rs=X({},Sr,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Fi,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Un&&(Un&&e.type==="mousemove"?(rl=e.screenX-Un.screenX,sl=e.screenY-Un.screenY):sl=rl=0,Un=e),rl)},movementY:function(e){return"movementY"in e?e.movementY:sl}}),Oo=Ie(Rs),fm=X({},Rs,{dataTransfer:0}),pm=Ie(fm),hm=X({},Sr,{relatedTarget:0}),ll=Ie(hm),gm=X({},Pn,{animationName:0,elapsedTime:0,pseudoElement:0}),xm=Ie(gm),ym=X({},Pn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),vm=Ie(ym),jm=X({},Pn,{data:0}),Uo=Ie(jm),wm={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Nm={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Sm={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function km(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Sm[e])?!!t[e]:!1}function Fi(){return km}var bm=X({},Sr,{key:function(e){if(e.key){var t=wm[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Yr(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Nm[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Fi,charCode:function(e){return e.type==="keypress"?Yr(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Yr(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Cm=Ie(bm),Em=X({},Rs,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Fo=Ie(Em),Lm=X({},Sr,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Fi}),Dm=Ie(Lm),Mm=X({},Pn,{propertyName:0,elapsedTime:0,pseudoElement:0}),Tm=Ie(Mm),Am=X({},Rs,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Im=Ie(Am),Pm=[9,13,27,32],Vi=it&&"CompositionEvent"in window,Yn=null;it&&"documentMode"in document&&(Yn=document.documentMode);var _m=it&&"TextEvent"in window&&!Yn,Ic=it&&(!Vi||Yn&&8<Yn&&11>=Yn),Vo=String.fromCharCode(32),Ho=!1;function Pc(e,t){switch(e){case"keyup":return Pm.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function _c(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var cn=!1;function zm(e,t){switch(e){case"compositionend":return _c(t);case"keypress":return t.which!==32?null:(Ho=!0,Vo);case"textInput":return e=t.data,e===Vo&&Ho?null:e;default:return null}}function Rm(e,t){if(cn)return e==="compositionend"||!Vi&&Pc(e,t)?(e=Ac(),qr=Oi=xt=null,cn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Ic&&t.locale!=="ko"?null:t.data;default:return null}}var $m={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Bo(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!$m[e.type]:t==="textarea"}function zc(e,t,n,r){fc(r),t=ms(t,"onChange"),0<t.length&&(n=new Ui("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Xn=null,cr=null;function Om(e){Kc(e,0)}function $s(e){var t=mn(e);if(ic(t))return e}function Um(e,t){if(e==="change")return t}var Rc=!1;if(it){var il;if(it){var ol="oninput"in document;if(!ol){var Wo=document.createElement("div");Wo.setAttribute("oninput","return;"),ol=typeof Wo.oninput=="function"}il=ol}else il=!1;Rc=il&&(!document.documentMode||9<document.documentMode)}function Qo(){Xn&&(Xn.detachEvent("onpropertychange",$c),cr=Xn=null)}function $c(e){if(e.propertyName==="value"&&$s(cr)){var t=[];zc(t,cr,e,Pi(e)),xc(Om,t)}}function Fm(e,t,n){e==="focusin"?(Qo(),Xn=t,cr=n,Xn.attachEvent("onpropertychange",$c)):e==="focusout"&&Qo()}function Vm(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return $s(cr)}function Hm(e,t){if(e==="click")return $s(t)}function Bm(e,t){if(e==="input"||e==="change")return $s(t)}function Wm(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Ke=typeof Object.is=="function"?Object.is:Wm;function ur(e,t){if(Ke(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var l=n[r];if(!Cl.call(t,l)||!Ke(e[l],t[l]))return!1}return!0}function Ko(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Go(e,t){var n=Ko(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Ko(n)}}function Oc(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Oc(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Uc(){for(var e=window,t=ls();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=ls(e.document)}return t}function Hi(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Qm(e){var t=Uc(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&Oc(n.ownerDocument.documentElement,n)){if(r!==null&&Hi(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var l=n.textContent.length,i=Math.min(r.start,l);r=r.end===void 0?i:Math.min(r.end,l),!e.extend&&i>r&&(l=r,r=i,i=l),l=Go(n,i);var o=Go(n,r);l&&o&&(e.rangeCount!==1||e.anchorNode!==l.node||e.anchorOffset!==l.offset||e.focusNode!==o.node||e.focusOffset!==o.offset)&&(t=t.createRange(),t.setStart(l.node,l.offset),e.removeAllRanges(),i>r?(e.addRange(t),e.extend(o.node,o.offset)):(t.setEnd(o.node,o.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Km=it&&"documentMode"in document&&11>=document.documentMode,un=null,Wl=null,Zn=null,Ql=!1;function qo(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Ql||un==null||un!==ls(r)||(r=un,"selectionStart"in r&&Hi(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Zn&&ur(Zn,r)||(Zn=r,r=ms(Wl,"onSelect"),0<r.length&&(t=new Ui("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=un)))}function zr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var dn={animationend:zr("Animation","AnimationEnd"),animationiteration:zr("Animation","AnimationIteration"),animationstart:zr("Animation","AnimationStart"),transitionend:zr("Transition","TransitionEnd")},al={},Fc={};it&&(Fc=document.createElement("div").style,"AnimationEvent"in window||(delete dn.animationend.animation,delete dn.animationiteration.animation,delete dn.animationstart.animation),"TransitionEvent"in window||delete dn.transitionend.transition);function Os(e){if(al[e])return al[e];if(!dn[e])return e;var t=dn[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Fc)return al[e]=t[n];return e}var Vc=Os("animationend"),Hc=Os("animationiteration"),Bc=Os("animationstart"),Wc=Os("transitionend"),Qc=new Map,Yo="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Tt(e,t){Qc.set(e,t),tn(t,[e])}for(var cl=0;cl<Yo.length;cl++){var ul=Yo[cl],Gm=ul.toLowerCase(),qm=ul[0].toUpperCase()+ul.slice(1);Tt(Gm,"on"+qm)}Tt(Vc,"onAnimationEnd");Tt(Hc,"onAnimationIteration");Tt(Bc,"onAnimationStart");Tt("dblclick","onDoubleClick");Tt("focusin","onFocus");Tt("focusout","onBlur");Tt(Wc,"onTransitionEnd");bn("onMouseEnter",["mouseout","mouseover"]);bn("onMouseLeave",["mouseout","mouseover"]);bn("onPointerEnter",["pointerout","pointerover"]);bn("onPointerLeave",["pointerout","pointerover"]);tn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));tn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));tn("onBeforeInput",["compositionend","keypress","textInput","paste"]);tn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));tn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));tn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Kn="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Ym=new Set("cancel close invalid load scroll toggle".split(" ").concat(Kn));function Xo(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,Gd(r,t,void 0,e),e.currentTarget=null}function Kc(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],l=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var o=r.length-1;0<=o;o--){var a=r[o],c=a.instance,f=a.currentTarget;if(a=a.listener,c!==i&&l.isPropagationStopped())break e;Xo(l,a,f),i=c}else for(o=0;o<r.length;o++){if(a=r[o],c=a.instance,f=a.currentTarget,a=a.listener,c!==i&&l.isPropagationStopped())break e;Xo(l,a,f),i=c}}}if(os)throw e=Fl,os=!1,Fl=null,e}function W(e,t){var n=t[Xl];n===void 0&&(n=t[Xl]=new Set);var r=e+"__bubble";n.has(r)||(Gc(t,e,2,!1),n.add(r))}function dl(e,t,n){var r=0;t&&(r|=4),Gc(n,e,r,t)}var Rr="_reactListening"+Math.random().toString(36).slice(2);function dr(e){if(!e[Rr]){e[Rr]=!0,tc.forEach(function(n){n!=="selectionchange"&&(Ym.has(n)||dl(n,!1,e),dl(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Rr]||(t[Rr]=!0,dl("selectionchange",!1,t))}}function Gc(e,t,n,r){switch(Tc(t)){case 1:var l=um;break;case 4:l=dm;break;default:l=$i}n=l.bind(null,t,n,e),l=void 0,!Ul||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(l=!0),r?l!==void 0?e.addEventListener(t,n,{capture:!0,passive:l}):e.addEventListener(t,n,!0):l!==void 0?e.addEventListener(t,n,{passive:l}):e.addEventListener(t,n,!1)}function ml(e,t,n,r,l){var i=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var o=r.tag;if(o===3||o===4){var a=r.stateNode.containerInfo;if(a===l||a.nodeType===8&&a.parentNode===l)break;if(o===4)for(o=r.return;o!==null;){var c=o.tag;if((c===3||c===4)&&(c=o.stateNode.containerInfo,c===l||c.nodeType===8&&c.parentNode===l))return;o=o.return}for(;a!==null;){if(o=Wt(a),o===null)return;if(c=o.tag,c===5||c===6){r=i=o;continue e}a=a.parentNode}}r=r.return}xc(function(){var f=i,x=Pi(n),g=[];e:{var y=Qc.get(e);if(y!==void 0){var v=Ui,N=e;switch(e){case"keypress":if(Yr(n)===0)break e;case"keydown":case"keyup":v=Cm;break;case"focusin":N="focus",v=ll;break;case"focusout":N="blur",v=ll;break;case"beforeblur":case"afterblur":v=ll;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":v=Oo;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":v=pm;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":v=Dm;break;case Vc:case Hc:case Bc:v=xm;break;case Wc:v=Tm;break;case"scroll":v=mm;break;case"wheel":v=Im;break;case"copy":case"cut":case"paste":v=vm;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":v=Fo}var h=(t&4)!==0,S=!h&&e==="scroll",m=h?y!==null?y+"Capture":null:y;h=[];for(var d=f,u;d!==null;){u=d;var p=u.stateNode;if(u.tag===5&&p!==null&&(u=p,m!==null&&(p=lr(d,m),p!=null&&h.push(mr(d,p,u)))),S)break;d=d.return}0<h.length&&(y=new v(y,N,null,n,x),g.push({event:y,listeners:h}))}}if(!(t&7)){e:{if(y=e==="mouseover"||e==="pointerover",v=e==="mouseout"||e==="pointerout",y&&n!==$l&&(N=n.relatedTarget||n.fromElement)&&(Wt(N)||N[ot]))break e;if((v||y)&&(y=x.window===x?x:(y=x.ownerDocument)?y.defaultView||y.parentWindow:window,v?(N=n.relatedTarget||n.toElement,v=f,N=N?Wt(N):null,N!==null&&(S=nn(N),N!==S||N.tag!==5&&N.tag!==6)&&(N=null)):(v=null,N=f),v!==N)){if(h=Oo,p="onMouseLeave",m="onMouseEnter",d="mouse",(e==="pointerout"||e==="pointerover")&&(h=Fo,p="onPointerLeave",m="onPointerEnter",d="pointer"),S=v==null?y:mn(v),u=N==null?y:mn(N),y=new h(p,d+"leave",v,n,x),y.target=S,y.relatedTarget=u,p=null,Wt(x)===f&&(h=new h(m,d+"enter",N,n,x),h.target=u,h.relatedTarget=S,p=h),S=p,v&&N)t:{for(h=v,m=N,d=0,u=h;u;u=ln(u))d++;for(u=0,p=m;p;p=ln(p))u++;for(;0<d-u;)h=ln(h),d--;for(;0<u-d;)m=ln(m),u--;for(;d--;){if(h===m||m!==null&&h===m.alternate)break t;h=ln(h),m=ln(m)}h=null}else h=null;v!==null&&Zo(g,y,v,h,!1),N!==null&&S!==null&&Zo(g,S,N,h,!0)}}e:{if(y=f?mn(f):window,v=y.nodeName&&y.nodeName.toLowerCase(),v==="select"||v==="input"&&y.type==="file")var j=Um;else if(Bo(y))if(Rc)j=Bm;else{j=Vm;var w=Fm}else(v=y.nodeName)&&v.toLowerCase()==="input"&&(y.type==="checkbox"||y.type==="radio")&&(j=Hm);if(j&&(j=j(e,f))){zc(g,j,n,x);break e}w&&w(e,y,f),e==="focusout"&&(w=y._wrapperState)&&w.controlled&&y.type==="number"&&Il(y,"number",y.value)}switch(w=f?mn(f):window,e){case"focusin":(Bo(w)||w.contentEditable==="true")&&(un=w,Wl=f,Zn=null);break;case"focusout":Zn=Wl=un=null;break;case"mousedown":Ql=!0;break;case"contextmenu":case"mouseup":case"dragend":Ql=!1,qo(g,n,x);break;case"selectionchange":if(Km)break;case"keydown":case"keyup":qo(g,n,x)}var M;if(Vi)e:{switch(e){case"compositionstart":var T="onCompositionStart";break e;case"compositionend":T="onCompositionEnd";break e;case"compositionupdate":T="onCompositionUpdate";break e}T=void 0}else cn?Pc(e,n)&&(T="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(T="onCompositionStart");T&&(Ic&&n.locale!=="ko"&&(cn||T!=="onCompositionStart"?T==="onCompositionEnd"&&cn&&(M=Ac()):(xt=x,Oi="value"in xt?xt.value:xt.textContent,cn=!0)),w=ms(f,T),0<w.length&&(T=new Uo(T,e,null,n,x),g.push({event:T,listeners:w}),M?T.data=M:(M=_c(n),M!==null&&(T.data=M)))),(M=_m?zm(e,n):Rm(e,n))&&(f=ms(f,"onBeforeInput"),0<f.length&&(x=new Uo("onBeforeInput","beforeinput",null,n,x),g.push({event:x,listeners:f}),x.data=M))}Kc(g,t)})}function mr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function ms(e,t){for(var n=t+"Capture",r=[];e!==null;){var l=e,i=l.stateNode;l.tag===5&&i!==null&&(l=i,i=lr(e,n),i!=null&&r.unshift(mr(e,i,l)),i=lr(e,t),i!=null&&r.push(mr(e,i,l))),e=e.return}return r}function ln(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Zo(e,t,n,r,l){for(var i=t._reactName,o=[];n!==null&&n!==r;){var a=n,c=a.alternate,f=a.stateNode;if(c!==null&&c===r)break;a.tag===5&&f!==null&&(a=f,l?(c=lr(n,i),c!=null&&o.unshift(mr(n,c,a))):l||(c=lr(n,i),c!=null&&o.push(mr(n,c,a)))),n=n.return}o.length!==0&&e.push({event:t,listeners:o})}var Xm=/\r\n?/g,Zm=/\u0000|\uFFFD/g;function Jo(e){return(typeof e=="string"?e:""+e).replace(Xm,`
`).replace(Zm,"")}function $r(e,t,n){if(t=Jo(t),Jo(e)!==t&&n)throw Error(b(425))}function fs(){}var Kl=null,Gl=null;function ql(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Yl=typeof setTimeout=="function"?setTimeout:void 0,Jm=typeof clearTimeout=="function"?clearTimeout:void 0,ea=typeof Promise=="function"?Promise:void 0,ef=typeof queueMicrotask=="function"?queueMicrotask:typeof ea<"u"?function(e){return ea.resolve(null).then(e).catch(tf)}:Yl;function tf(e){setTimeout(function(){throw e})}function fl(e,t){var n=t,r=0;do{var l=n.nextSibling;if(e.removeChild(n),l&&l.nodeType===8)if(n=l.data,n==="/$"){if(r===0){e.removeChild(l),ar(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=l}while(n);ar(t)}function Nt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function ta(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var _n=Math.random().toString(36).slice(2),Ye="__reactFiber$"+_n,fr="__reactProps$"+_n,ot="__reactContainer$"+_n,Xl="__reactEvents$"+_n,nf="__reactListeners$"+_n,rf="__reactHandles$"+_n;function Wt(e){var t=e[Ye];if(t)return t;for(var n=e.parentNode;n;){if(t=n[ot]||n[Ye]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=ta(e);e!==null;){if(n=e[Ye])return n;e=ta(e)}return t}e=n,n=e.parentNode}return null}function kr(e){return e=e[Ye]||e[ot],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function mn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(b(33))}function Us(e){return e[fr]||null}var Zl=[],fn=-1;function At(e){return{current:e}}function Q(e){0>fn||(e.current=Zl[fn],Zl[fn]=null,fn--)}function B(e,t){fn++,Zl[fn]=e.current,e.current=t}var Mt={},he=At(Mt),ke=At(!1),Yt=Mt;function Cn(e,t){var n=e.type.contextTypes;if(!n)return Mt;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var l={},i;for(i in n)l[i]=t[i];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=l),l}function be(e){return e=e.childContextTypes,e!=null}function ps(){Q(ke),Q(he)}function na(e,t,n){if(he.current!==Mt)throw Error(b(168));B(he,t),B(ke,n)}function qc(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var l in r)if(!(l in t))throw Error(b(108,Fd(e)||"Unknown",l));return X({},n,r)}function hs(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Mt,Yt=he.current,B(he,e),B(ke,ke.current),!0}function ra(e,t,n){var r=e.stateNode;if(!r)throw Error(b(169));n?(e=qc(e,t,Yt),r.__reactInternalMemoizedMergedChildContext=e,Q(ke),Q(he),B(he,e)):Q(ke),B(ke,n)}var et=null,Fs=!1,pl=!1;function Yc(e){et===null?et=[e]:et.push(e)}function sf(e){Fs=!0,Yc(e)}function It(){if(!pl&&et!==null){pl=!0;var e=0,t=F;try{var n=et;for(F=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}et=null,Fs=!1}catch(l){throw et!==null&&(et=et.slice(e+1)),wc(_i,It),l}finally{F=t,pl=!1}}return null}var pn=[],hn=0,gs=null,xs=0,Pe=[],_e=0,Xt=null,nt=1,rt="";function Ot(e,t){pn[hn++]=xs,pn[hn++]=gs,gs=e,xs=t}function Xc(e,t,n){Pe[_e++]=nt,Pe[_e++]=rt,Pe[_e++]=Xt,Xt=e;var r=nt;e=rt;var l=32-We(r)-1;r&=~(1<<l),n+=1;var i=32-We(t)+l;if(30<i){var o=l-l%5;i=(r&(1<<o)-1).toString(32),r>>=o,l-=o,nt=1<<32-We(t)+l|n<<l|r,rt=i+e}else nt=1<<i|n<<l|r,rt=e}function Bi(e){e.return!==null&&(Ot(e,1),Xc(e,1,0))}function Wi(e){for(;e===gs;)gs=pn[--hn],pn[hn]=null,xs=pn[--hn],pn[hn]=null;for(;e===Xt;)Xt=Pe[--_e],Pe[_e]=null,rt=Pe[--_e],Pe[_e]=null,nt=Pe[--_e],Pe[_e]=null}var Me=null,De=null,G=!1,He=null;function Zc(e,t){var n=ze(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function sa(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Me=e,De=Nt(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Me=e,De=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=Xt!==null?{id:nt,overflow:rt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=ze(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,Me=e,De=null,!0):!1;default:return!1}}function Jl(e){return(e.mode&1)!==0&&(e.flags&128)===0}function ei(e){if(G){var t=De;if(t){var n=t;if(!sa(e,t)){if(Jl(e))throw Error(b(418));t=Nt(n.nextSibling);var r=Me;t&&sa(e,t)?Zc(r,n):(e.flags=e.flags&-4097|2,G=!1,Me=e)}}else{if(Jl(e))throw Error(b(418));e.flags=e.flags&-4097|2,G=!1,Me=e}}}function la(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Me=e}function Or(e){if(e!==Me)return!1;if(!G)return la(e),G=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!ql(e.type,e.memoizedProps)),t&&(t=De)){if(Jl(e))throw Jc(),Error(b(418));for(;t;)Zc(e,t),t=Nt(t.nextSibling)}if(la(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(b(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){De=Nt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}De=null}}else De=Me?Nt(e.stateNode.nextSibling):null;return!0}function Jc(){for(var e=De;e;)e=Nt(e.nextSibling)}function En(){De=Me=null,G=!1}function Qi(e){He===null?He=[e]:He.push(e)}var lf=ut.ReactCurrentBatchConfig;function Fn(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(b(309));var r=n.stateNode}if(!r)throw Error(b(147,e));var l=r,i=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===i?t.ref:(t=function(o){var a=l.refs;o===null?delete a[i]:a[i]=o},t._stringRef=i,t)}if(typeof e!="string")throw Error(b(284));if(!n._owner)throw Error(b(290,e))}return e}function Ur(e,t){throw e=Object.prototype.toString.call(t),Error(b(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function ia(e){var t=e._init;return t(e._payload)}function eu(e){function t(m,d){if(e){var u=m.deletions;u===null?(m.deletions=[d],m.flags|=16):u.push(d)}}function n(m,d){if(!e)return null;for(;d!==null;)t(m,d),d=d.sibling;return null}function r(m,d){for(m=new Map;d!==null;)d.key!==null?m.set(d.key,d):m.set(d.index,d),d=d.sibling;return m}function l(m,d){return m=Ct(m,d),m.index=0,m.sibling=null,m}function i(m,d,u){return m.index=u,e?(u=m.alternate,u!==null?(u=u.index,u<d?(m.flags|=2,d):u):(m.flags|=2,d)):(m.flags|=1048576,d)}function o(m){return e&&m.alternate===null&&(m.flags|=2),m}function a(m,d,u,p){return d===null||d.tag!==6?(d=wl(u,m.mode,p),d.return=m,d):(d=l(d,u),d.return=m,d)}function c(m,d,u,p){var j=u.type;return j===an?x(m,d,u.props.children,p,u.key):d!==null&&(d.elementType===j||typeof j=="object"&&j!==null&&j.$$typeof===ft&&ia(j)===d.type)?(p=l(d,u.props),p.ref=Fn(m,d,u),p.return=m,p):(p=rs(u.type,u.key,u.props,null,m.mode,p),p.ref=Fn(m,d,u),p.return=m,p)}function f(m,d,u,p){return d===null||d.tag!==4||d.stateNode.containerInfo!==u.containerInfo||d.stateNode.implementation!==u.implementation?(d=Nl(u,m.mode,p),d.return=m,d):(d=l(d,u.children||[]),d.return=m,d)}function x(m,d,u,p,j){return d===null||d.tag!==7?(d=qt(u,m.mode,p,j),d.return=m,d):(d=l(d,u),d.return=m,d)}function g(m,d,u){if(typeof d=="string"&&d!==""||typeof d=="number")return d=wl(""+d,m.mode,u),d.return=m,d;if(typeof d=="object"&&d!==null){switch(d.$$typeof){case Dr:return u=rs(d.type,d.key,d.props,null,m.mode,u),u.ref=Fn(m,null,d),u.return=m,u;case on:return d=Nl(d,m.mode,u),d.return=m,d;case ft:var p=d._init;return g(m,p(d._payload),u)}if(Wn(d)||zn(d))return d=qt(d,m.mode,u,null),d.return=m,d;Ur(m,d)}return null}function y(m,d,u,p){var j=d!==null?d.key:null;if(typeof u=="string"&&u!==""||typeof u=="number")return j!==null?null:a(m,d,""+u,p);if(typeof u=="object"&&u!==null){switch(u.$$typeof){case Dr:return u.key===j?c(m,d,u,p):null;case on:return u.key===j?f(m,d,u,p):null;case ft:return j=u._init,y(m,d,j(u._payload),p)}if(Wn(u)||zn(u))return j!==null?null:x(m,d,u,p,null);Ur(m,u)}return null}function v(m,d,u,p,j){if(typeof p=="string"&&p!==""||typeof p=="number")return m=m.get(u)||null,a(d,m,""+p,j);if(typeof p=="object"&&p!==null){switch(p.$$typeof){case Dr:return m=m.get(p.key===null?u:p.key)||null,c(d,m,p,j);case on:return m=m.get(p.key===null?u:p.key)||null,f(d,m,p,j);case ft:var w=p._init;return v(m,d,u,w(p._payload),j)}if(Wn(p)||zn(p))return m=m.get(u)||null,x(d,m,p,j,null);Ur(d,p)}return null}function N(m,d,u,p){for(var j=null,w=null,M=d,T=d=0,O=null;M!==null&&T<u.length;T++){M.index>T?(O=M,M=null):O=M.sibling;var _=y(m,M,u[T],p);if(_===null){M===null&&(M=O);break}e&&M&&_.alternate===null&&t(m,M),d=i(_,d,T),w===null?j=_:w.sibling=_,w=_,M=O}if(T===u.length)return n(m,M),G&&Ot(m,T),j;if(M===null){for(;T<u.length;T++)M=g(m,u[T],p),M!==null&&(d=i(M,d,T),w===null?j=M:w.sibling=M,w=M);return G&&Ot(m,T),j}for(M=r(m,M);T<u.length;T++)O=v(M,m,T,u[T],p),O!==null&&(e&&O.alternate!==null&&M.delete(O.key===null?T:O.key),d=i(O,d,T),w===null?j=O:w.sibling=O,w=O);return e&&M.forEach(function(we){return t(m,we)}),G&&Ot(m,T),j}function h(m,d,u,p){var j=zn(u);if(typeof j!="function")throw Error(b(150));if(u=j.call(u),u==null)throw Error(b(151));for(var w=j=null,M=d,T=d=0,O=null,_=u.next();M!==null&&!_.done;T++,_=u.next()){M.index>T?(O=M,M=null):O=M.sibling;var we=y(m,M,_.value,p);if(we===null){M===null&&(M=O);break}e&&M&&we.alternate===null&&t(m,M),d=i(we,d,T),w===null?j=we:w.sibling=we,w=we,M=O}if(_.done)return n(m,M),G&&Ot(m,T),j;if(M===null){for(;!_.done;T++,_=u.next())_=g(m,_.value,p),_!==null&&(d=i(_,d,T),w===null?j=_:w.sibling=_,w=_);return G&&Ot(m,T),j}for(M=r(m,M);!_.done;T++,_=u.next())_=v(M,m,T,_.value,p),_!==null&&(e&&_.alternate!==null&&M.delete(_.key===null?T:_.key),d=i(_,d,T),w===null?j=_:w.sibling=_,w=_);return e&&M.forEach(function(_t){return t(m,_t)}),G&&Ot(m,T),j}function S(m,d,u,p){if(typeof u=="object"&&u!==null&&u.type===an&&u.key===null&&(u=u.props.children),typeof u=="object"&&u!==null){switch(u.$$typeof){case Dr:e:{for(var j=u.key,w=d;w!==null;){if(w.key===j){if(j=u.type,j===an){if(w.tag===7){n(m,w.sibling),d=l(w,u.props.children),d.return=m,m=d;break e}}else if(w.elementType===j||typeof j=="object"&&j!==null&&j.$$typeof===ft&&ia(j)===w.type){n(m,w.sibling),d=l(w,u.props),d.ref=Fn(m,w,u),d.return=m,m=d;break e}n(m,w);break}else t(m,w);w=w.sibling}u.type===an?(d=qt(u.props.children,m.mode,p,u.key),d.return=m,m=d):(p=rs(u.type,u.key,u.props,null,m.mode,p),p.ref=Fn(m,d,u),p.return=m,m=p)}return o(m);case on:e:{for(w=u.key;d!==null;){if(d.key===w)if(d.tag===4&&d.stateNode.containerInfo===u.containerInfo&&d.stateNode.implementation===u.implementation){n(m,d.sibling),d=l(d,u.children||[]),d.return=m,m=d;break e}else{n(m,d);break}else t(m,d);d=d.sibling}d=Nl(u,m.mode,p),d.return=m,m=d}return o(m);case ft:return w=u._init,S(m,d,w(u._payload),p)}if(Wn(u))return N(m,d,u,p);if(zn(u))return h(m,d,u,p);Ur(m,u)}return typeof u=="string"&&u!==""||typeof u=="number"?(u=""+u,d!==null&&d.tag===6?(n(m,d.sibling),d=l(d,u),d.return=m,m=d):(n(m,d),d=wl(u,m.mode,p),d.return=m,m=d),o(m)):n(m,d)}return S}var Ln=eu(!0),tu=eu(!1),ys=At(null),vs=null,gn=null,Ki=null;function Gi(){Ki=gn=vs=null}function qi(e){var t=ys.current;Q(ys),e._currentValue=t}function ti(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Sn(e,t){vs=e,Ki=gn=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Se=!0),e.firstContext=null)}function $e(e){var t=e._currentValue;if(Ki!==e)if(e={context:e,memoizedValue:t,next:null},gn===null){if(vs===null)throw Error(b(308));gn=e,vs.dependencies={lanes:0,firstContext:e}}else gn=gn.next=e;return t}var Qt=null;function Yi(e){Qt===null?Qt=[e]:Qt.push(e)}function nu(e,t,n,r){var l=t.interleaved;return l===null?(n.next=n,Yi(t)):(n.next=l.next,l.next=n),t.interleaved=n,at(e,r)}function at(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var pt=!1;function Xi(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function ru(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function lt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function St(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,$&2){var l=r.pending;return l===null?t.next=t:(t.next=l.next,l.next=t),r.pending=t,at(e,n)}return l=r.interleaved,l===null?(t.next=t,Yi(r)):(t.next=l.next,l.next=t),r.interleaved=t,at(e,n)}function Xr(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,zi(e,n)}}function oa(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var l=null,i=null;if(n=n.firstBaseUpdate,n!==null){do{var o={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};i===null?l=i=o:i=i.next=o,n=n.next}while(n!==null);i===null?l=i=t:i=i.next=t}else l=i=t;n={baseState:r.baseState,firstBaseUpdate:l,lastBaseUpdate:i,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function js(e,t,n,r){var l=e.updateQueue;pt=!1;var i=l.firstBaseUpdate,o=l.lastBaseUpdate,a=l.shared.pending;if(a!==null){l.shared.pending=null;var c=a,f=c.next;c.next=null,o===null?i=f:o.next=f,o=c;var x=e.alternate;x!==null&&(x=x.updateQueue,a=x.lastBaseUpdate,a!==o&&(a===null?x.firstBaseUpdate=f:a.next=f,x.lastBaseUpdate=c))}if(i!==null){var g=l.baseState;o=0,x=f=c=null,a=i;do{var y=a.lane,v=a.eventTime;if((r&y)===y){x!==null&&(x=x.next={eventTime:v,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});e:{var N=e,h=a;switch(y=t,v=n,h.tag){case 1:if(N=h.payload,typeof N=="function"){g=N.call(v,g,y);break e}g=N;break e;case 3:N.flags=N.flags&-65537|128;case 0:if(N=h.payload,y=typeof N=="function"?N.call(v,g,y):N,y==null)break e;g=X({},g,y);break e;case 2:pt=!0}}a.callback!==null&&a.lane!==0&&(e.flags|=64,y=l.effects,y===null?l.effects=[a]:y.push(a))}else v={eventTime:v,lane:y,tag:a.tag,payload:a.payload,callback:a.callback,next:null},x===null?(f=x=v,c=g):x=x.next=v,o|=y;if(a=a.next,a===null){if(a=l.shared.pending,a===null)break;y=a,a=y.next,y.next=null,l.lastBaseUpdate=y,l.shared.pending=null}}while(1);if(x===null&&(c=g),l.baseState=c,l.firstBaseUpdate=f,l.lastBaseUpdate=x,t=l.shared.interleaved,t!==null){l=t;do o|=l.lane,l=l.next;while(l!==t)}else i===null&&(l.shared.lanes=0);Jt|=o,e.lanes=o,e.memoizedState=g}}function aa(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],l=r.callback;if(l!==null){if(r.callback=null,r=n,typeof l!="function")throw Error(b(191,l));l.call(r)}}}var br={},Ze=At(br),pr=At(br),hr=At(br);function Kt(e){if(e===br)throw Error(b(174));return e}function Zi(e,t){switch(B(hr,t),B(pr,e),B(Ze,br),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:_l(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=_l(t,e)}Q(Ze),B(Ze,t)}function Dn(){Q(Ze),Q(pr),Q(hr)}function su(e){Kt(hr.current);var t=Kt(Ze.current),n=_l(t,e.type);t!==n&&(B(pr,e),B(Ze,n))}function Ji(e){pr.current===e&&(Q(Ze),Q(pr))}var q=At(0);function ws(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var hl=[];function eo(){for(var e=0;e<hl.length;e++)hl[e]._workInProgressVersionPrimary=null;hl.length=0}var Zr=ut.ReactCurrentDispatcher,gl=ut.ReactCurrentBatchConfig,Zt=0,Y=null,ne=null,se=null,Ns=!1,Jn=!1,gr=0,of=0;function me(){throw Error(b(321))}function to(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Ke(e[n],t[n]))return!1;return!0}function no(e,t,n,r,l,i){if(Zt=i,Y=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Zr.current=e===null||e.memoizedState===null?df:mf,e=n(r,l),Jn){i=0;do{if(Jn=!1,gr=0,25<=i)throw Error(b(301));i+=1,se=ne=null,t.updateQueue=null,Zr.current=ff,e=n(r,l)}while(Jn)}if(Zr.current=Ss,t=ne!==null&&ne.next!==null,Zt=0,se=ne=Y=null,Ns=!1,t)throw Error(b(300));return e}function ro(){var e=gr!==0;return gr=0,e}function qe(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return se===null?Y.memoizedState=se=e:se=se.next=e,se}function Oe(){if(ne===null){var e=Y.alternate;e=e!==null?e.memoizedState:null}else e=ne.next;var t=se===null?Y.memoizedState:se.next;if(t!==null)se=t,ne=e;else{if(e===null)throw Error(b(310));ne=e,e={memoizedState:ne.memoizedState,baseState:ne.baseState,baseQueue:ne.baseQueue,queue:ne.queue,next:null},se===null?Y.memoizedState=se=e:se=se.next=e}return se}function xr(e,t){return typeof t=="function"?t(e):t}function xl(e){var t=Oe(),n=t.queue;if(n===null)throw Error(b(311));n.lastRenderedReducer=e;var r=ne,l=r.baseQueue,i=n.pending;if(i!==null){if(l!==null){var o=l.next;l.next=i.next,i.next=o}r.baseQueue=l=i,n.pending=null}if(l!==null){i=l.next,r=r.baseState;var a=o=null,c=null,f=i;do{var x=f.lane;if((Zt&x)===x)c!==null&&(c=c.next={lane:0,action:f.action,hasEagerState:f.hasEagerState,eagerState:f.eagerState,next:null}),r=f.hasEagerState?f.eagerState:e(r,f.action);else{var g={lane:x,action:f.action,hasEagerState:f.hasEagerState,eagerState:f.eagerState,next:null};c===null?(a=c=g,o=r):c=c.next=g,Y.lanes|=x,Jt|=x}f=f.next}while(f!==null&&f!==i);c===null?o=r:c.next=a,Ke(r,t.memoizedState)||(Se=!0),t.memoizedState=r,t.baseState=o,t.baseQueue=c,n.lastRenderedState=r}if(e=n.interleaved,e!==null){l=e;do i=l.lane,Y.lanes|=i,Jt|=i,l=l.next;while(l!==e)}else l===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function yl(e){var t=Oe(),n=t.queue;if(n===null)throw Error(b(311));n.lastRenderedReducer=e;var r=n.dispatch,l=n.pending,i=t.memoizedState;if(l!==null){n.pending=null;var o=l=l.next;do i=e(i,o.action),o=o.next;while(o!==l);Ke(i,t.memoizedState)||(Se=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function lu(){}function iu(e,t){var n=Y,r=Oe(),l=t(),i=!Ke(r.memoizedState,l);if(i&&(r.memoizedState=l,Se=!0),r=r.queue,so(cu.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||se!==null&&se.memoizedState.tag&1){if(n.flags|=2048,yr(9,au.bind(null,n,r,l,t),void 0,null),le===null)throw Error(b(349));Zt&30||ou(n,t,l)}return l}function ou(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=Y.updateQueue,t===null?(t={lastEffect:null,stores:null},Y.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function au(e,t,n,r){t.value=n,t.getSnapshot=r,uu(t)&&du(e)}function cu(e,t,n){return n(function(){uu(t)&&du(e)})}function uu(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Ke(e,n)}catch{return!0}}function du(e){var t=at(e,1);t!==null&&Qe(t,e,1,-1)}function ca(e){var t=qe();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:xr,lastRenderedState:e},t.queue=e,e=e.dispatch=uf.bind(null,Y,e),[t.memoizedState,e]}function yr(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=Y.updateQueue,t===null?(t={lastEffect:null,stores:null},Y.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function mu(){return Oe().memoizedState}function Jr(e,t,n,r){var l=qe();Y.flags|=e,l.memoizedState=yr(1|t,n,void 0,r===void 0?null:r)}function Vs(e,t,n,r){var l=Oe();r=r===void 0?null:r;var i=void 0;if(ne!==null){var o=ne.memoizedState;if(i=o.destroy,r!==null&&to(r,o.deps)){l.memoizedState=yr(t,n,i,r);return}}Y.flags|=e,l.memoizedState=yr(1|t,n,i,r)}function ua(e,t){return Jr(8390656,8,e,t)}function so(e,t){return Vs(2048,8,e,t)}function fu(e,t){return Vs(4,2,e,t)}function pu(e,t){return Vs(4,4,e,t)}function hu(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function gu(e,t,n){return n=n!=null?n.concat([e]):null,Vs(4,4,hu.bind(null,t,e),n)}function lo(){}function xu(e,t){var n=Oe();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&to(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function yu(e,t){var n=Oe();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&to(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function vu(e,t,n){return Zt&21?(Ke(n,t)||(n=kc(),Y.lanes|=n,Jt|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Se=!0),e.memoizedState=n)}function af(e,t){var n=F;F=n!==0&&4>n?n:4,e(!0);var r=gl.transition;gl.transition={};try{e(!1),t()}finally{F=n,gl.transition=r}}function ju(){return Oe().memoizedState}function cf(e,t,n){var r=bt(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},wu(e))Nu(t,n);else if(n=nu(e,t,n,r),n!==null){var l=ye();Qe(n,e,r,l),Su(n,t,r)}}function uf(e,t,n){var r=bt(e),l={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(wu(e))Nu(t,l);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var o=t.lastRenderedState,a=i(o,n);if(l.hasEagerState=!0,l.eagerState=a,Ke(a,o)){var c=t.interleaved;c===null?(l.next=l,Yi(t)):(l.next=c.next,c.next=l),t.interleaved=l;return}}catch{}finally{}n=nu(e,t,l,r),n!==null&&(l=ye(),Qe(n,e,r,l),Su(n,t,r))}}function wu(e){var t=e.alternate;return e===Y||t!==null&&t===Y}function Nu(e,t){Jn=Ns=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Su(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,zi(e,n)}}var Ss={readContext:$e,useCallback:me,useContext:me,useEffect:me,useImperativeHandle:me,useInsertionEffect:me,useLayoutEffect:me,useMemo:me,useReducer:me,useRef:me,useState:me,useDebugValue:me,useDeferredValue:me,useTransition:me,useMutableSource:me,useSyncExternalStore:me,useId:me,unstable_isNewReconciler:!1},df={readContext:$e,useCallback:function(e,t){return qe().memoizedState=[e,t===void 0?null:t],e},useContext:$e,useEffect:ua,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,Jr(4194308,4,hu.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Jr(4194308,4,e,t)},useInsertionEffect:function(e,t){return Jr(4,2,e,t)},useMemo:function(e,t){var n=qe();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=qe();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=cf.bind(null,Y,e),[r.memoizedState,e]},useRef:function(e){var t=qe();return e={current:e},t.memoizedState=e},useState:ca,useDebugValue:lo,useDeferredValue:function(e){return qe().memoizedState=e},useTransition:function(){var e=ca(!1),t=e[0];return e=af.bind(null,e[1]),qe().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=Y,l=qe();if(G){if(n===void 0)throw Error(b(407));n=n()}else{if(n=t(),le===null)throw Error(b(349));Zt&30||ou(r,t,n)}l.memoizedState=n;var i={value:n,getSnapshot:t};return l.queue=i,ua(cu.bind(null,r,i,e),[e]),r.flags|=2048,yr(9,au.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=qe(),t=le.identifierPrefix;if(G){var n=rt,r=nt;n=(r&~(1<<32-We(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=gr++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=of++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},mf={readContext:$e,useCallback:xu,useContext:$e,useEffect:so,useImperativeHandle:gu,useInsertionEffect:fu,useLayoutEffect:pu,useMemo:yu,useReducer:xl,useRef:mu,useState:function(){return xl(xr)},useDebugValue:lo,useDeferredValue:function(e){var t=Oe();return vu(t,ne.memoizedState,e)},useTransition:function(){var e=xl(xr)[0],t=Oe().memoizedState;return[e,t]},useMutableSource:lu,useSyncExternalStore:iu,useId:ju,unstable_isNewReconciler:!1},ff={readContext:$e,useCallback:xu,useContext:$e,useEffect:so,useImperativeHandle:gu,useInsertionEffect:fu,useLayoutEffect:pu,useMemo:yu,useReducer:yl,useRef:mu,useState:function(){return yl(xr)},useDebugValue:lo,useDeferredValue:function(e){var t=Oe();return ne===null?t.memoizedState=e:vu(t,ne.memoizedState,e)},useTransition:function(){var e=yl(xr)[0],t=Oe().memoizedState;return[e,t]},useMutableSource:lu,useSyncExternalStore:iu,useId:ju,unstable_isNewReconciler:!1};function Fe(e,t){if(e&&e.defaultProps){t=X({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function ni(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:X({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Hs={isMounted:function(e){return(e=e._reactInternals)?nn(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=ye(),l=bt(e),i=lt(r,l);i.payload=t,n!=null&&(i.callback=n),t=St(e,i,l),t!==null&&(Qe(t,e,l,r),Xr(t,e,l))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=ye(),l=bt(e),i=lt(r,l);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=St(e,i,l),t!==null&&(Qe(t,e,l,r),Xr(t,e,l))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=ye(),r=bt(e),l=lt(n,r);l.tag=2,t!=null&&(l.callback=t),t=St(e,l,r),t!==null&&(Qe(t,e,r,n),Xr(t,e,r))}};function da(e,t,n,r,l,i,o){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,i,o):t.prototype&&t.prototype.isPureReactComponent?!ur(n,r)||!ur(l,i):!0}function ku(e,t,n){var r=!1,l=Mt,i=t.contextType;return typeof i=="object"&&i!==null?i=$e(i):(l=be(t)?Yt:he.current,r=t.contextTypes,i=(r=r!=null)?Cn(e,l):Mt),t=new t(n,i),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Hs,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=l,e.__reactInternalMemoizedMaskedChildContext=i),t}function ma(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Hs.enqueueReplaceState(t,t.state,null)}function ri(e,t,n,r){var l=e.stateNode;l.props=n,l.state=e.memoizedState,l.refs={},Xi(e);var i=t.contextType;typeof i=="object"&&i!==null?l.context=$e(i):(i=be(t)?Yt:he.current,l.context=Cn(e,i)),l.state=e.memoizedState,i=t.getDerivedStateFromProps,typeof i=="function"&&(ni(e,t,i,n),l.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof l.getSnapshotBeforeUpdate=="function"||typeof l.UNSAFE_componentWillMount!="function"&&typeof l.componentWillMount!="function"||(t=l.state,typeof l.componentWillMount=="function"&&l.componentWillMount(),typeof l.UNSAFE_componentWillMount=="function"&&l.UNSAFE_componentWillMount(),t!==l.state&&Hs.enqueueReplaceState(l,l.state,null),js(e,n,l,r),l.state=e.memoizedState),typeof l.componentDidMount=="function"&&(e.flags|=4194308)}function Mn(e,t){try{var n="",r=t;do n+=Ud(r),r=r.return;while(r);var l=n}catch(i){l=`
Error generating stack: `+i.message+`
`+i.stack}return{value:e,source:t,stack:l,digest:null}}function vl(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function si(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var pf=typeof WeakMap=="function"?WeakMap:Map;function bu(e,t,n){n=lt(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){bs||(bs=!0,pi=r),si(e,t)},n}function Cu(e,t,n){n=lt(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var l=t.value;n.payload=function(){return r(l)},n.callback=function(){si(e,t)}}var i=e.stateNode;return i!==null&&typeof i.componentDidCatch=="function"&&(n.callback=function(){si(e,t),typeof r!="function"&&(kt===null?kt=new Set([this]):kt.add(this));var o=t.stack;this.componentDidCatch(t.value,{componentStack:o!==null?o:""})}),n}function fa(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new pf;var l=new Set;r.set(t,l)}else l=r.get(t),l===void 0&&(l=new Set,r.set(t,l));l.has(n)||(l.add(n),e=Lf.bind(null,e,t,n),t.then(e,e))}function pa(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function ha(e,t,n,r,l){return e.mode&1?(e.flags|=65536,e.lanes=l,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=lt(-1,1),t.tag=2,St(n,t,1))),n.lanes|=1),e)}var hf=ut.ReactCurrentOwner,Se=!1;function xe(e,t,n,r){t.child=e===null?tu(t,null,n,r):Ln(t,e.child,n,r)}function ga(e,t,n,r,l){n=n.render;var i=t.ref;return Sn(t,l),r=no(e,t,n,r,i,l),n=ro(),e!==null&&!Se?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l,ct(e,t,l)):(G&&n&&Bi(t),t.flags|=1,xe(e,t,r,l),t.child)}function xa(e,t,n,r,l){if(e===null){var i=n.type;return typeof i=="function"&&!po(i)&&i.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=i,Eu(e,t,i,r,l)):(e=rs(n.type,null,r,t,t.mode,l),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!(e.lanes&l)){var o=i.memoizedProps;if(n=n.compare,n=n!==null?n:ur,n(o,r)&&e.ref===t.ref)return ct(e,t,l)}return t.flags|=1,e=Ct(i,r),e.ref=t.ref,e.return=t,t.child=e}function Eu(e,t,n,r,l){if(e!==null){var i=e.memoizedProps;if(ur(i,r)&&e.ref===t.ref)if(Se=!1,t.pendingProps=r=i,(e.lanes&l)!==0)e.flags&131072&&(Se=!0);else return t.lanes=e.lanes,ct(e,t,l)}return li(e,t,n,r,l)}function Lu(e,t,n){var r=t.pendingProps,l=r.children,i=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},B(yn,Le),Le|=n;else{if(!(n&1073741824))return e=i!==null?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,B(yn,Le),Le|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=i!==null?i.baseLanes:n,B(yn,Le),Le|=r}else i!==null?(r=i.baseLanes|n,t.memoizedState=null):r=n,B(yn,Le),Le|=r;return xe(e,t,l,n),t.child}function Du(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function li(e,t,n,r,l){var i=be(n)?Yt:he.current;return i=Cn(t,i),Sn(t,l),n=no(e,t,n,r,i,l),r=ro(),e!==null&&!Se?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l,ct(e,t,l)):(G&&r&&Bi(t),t.flags|=1,xe(e,t,n,l),t.child)}function ya(e,t,n,r,l){if(be(n)){var i=!0;hs(t)}else i=!1;if(Sn(t,l),t.stateNode===null)es(e,t),ku(t,n,r),ri(t,n,r,l),r=!0;else if(e===null){var o=t.stateNode,a=t.memoizedProps;o.props=a;var c=o.context,f=n.contextType;typeof f=="object"&&f!==null?f=$e(f):(f=be(n)?Yt:he.current,f=Cn(t,f));var x=n.getDerivedStateFromProps,g=typeof x=="function"||typeof o.getSnapshotBeforeUpdate=="function";g||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(a!==r||c!==f)&&ma(t,o,r,f),pt=!1;var y=t.memoizedState;o.state=y,js(t,r,o,l),c=t.memoizedState,a!==r||y!==c||ke.current||pt?(typeof x=="function"&&(ni(t,n,x,r),c=t.memoizedState),(a=pt||da(t,n,a,r,y,c,f))?(g||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount()),typeof o.componentDidMount=="function"&&(t.flags|=4194308)):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=c),o.props=r,o.state=c,o.context=f,r=a):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{o=t.stateNode,ru(e,t),a=t.memoizedProps,f=t.type===t.elementType?a:Fe(t.type,a),o.props=f,g=t.pendingProps,y=o.context,c=n.contextType,typeof c=="object"&&c!==null?c=$e(c):(c=be(n)?Yt:he.current,c=Cn(t,c));var v=n.getDerivedStateFromProps;(x=typeof v=="function"||typeof o.getSnapshotBeforeUpdate=="function")||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(a!==g||y!==c)&&ma(t,o,r,c),pt=!1,y=t.memoizedState,o.state=y,js(t,r,o,l);var N=t.memoizedState;a!==g||y!==N||ke.current||pt?(typeof v=="function"&&(ni(t,n,v,r),N=t.memoizedState),(f=pt||da(t,n,f,r,y,N,c)||!1)?(x||typeof o.UNSAFE_componentWillUpdate!="function"&&typeof o.componentWillUpdate!="function"||(typeof o.componentWillUpdate=="function"&&o.componentWillUpdate(r,N,c),typeof o.UNSAFE_componentWillUpdate=="function"&&o.UNSAFE_componentWillUpdate(r,N,c)),typeof o.componentDidUpdate=="function"&&(t.flags|=4),typeof o.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof o.componentDidUpdate!="function"||a===e.memoizedProps&&y===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&y===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=N),o.props=r,o.state=N,o.context=c,r=f):(typeof o.componentDidUpdate!="function"||a===e.memoizedProps&&y===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&y===e.memoizedState||(t.flags|=1024),r=!1)}return ii(e,t,n,r,i,l)}function ii(e,t,n,r,l,i){Du(e,t);var o=(t.flags&128)!==0;if(!r&&!o)return l&&ra(t,n,!1),ct(e,t,i);r=t.stateNode,hf.current=t;var a=o&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&o?(t.child=Ln(t,e.child,null,i),t.child=Ln(t,null,a,i)):xe(e,t,a,i),t.memoizedState=r.state,l&&ra(t,n,!0),t.child}function Mu(e){var t=e.stateNode;t.pendingContext?na(e,t.pendingContext,t.pendingContext!==t.context):t.context&&na(e,t.context,!1),Zi(e,t.containerInfo)}function va(e,t,n,r,l){return En(),Qi(l),t.flags|=256,xe(e,t,n,r),t.child}var oi={dehydrated:null,treeContext:null,retryLane:0};function ai(e){return{baseLanes:e,cachePool:null,transitions:null}}function Tu(e,t,n){var r=t.pendingProps,l=q.current,i=!1,o=(t.flags&128)!==0,a;if((a=o)||(a=e!==null&&e.memoizedState===null?!1:(l&2)!==0),a?(i=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(l|=1),B(q,l&1),e===null)return ei(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(o=r.children,e=r.fallback,i?(r=t.mode,i=t.child,o={mode:"hidden",children:o},!(r&1)&&i!==null?(i.childLanes=0,i.pendingProps=o):i=Qs(o,r,0,null),e=qt(e,r,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=ai(n),t.memoizedState=oi,e):io(t,o));if(l=e.memoizedState,l!==null&&(a=l.dehydrated,a!==null))return gf(e,t,o,r,a,l,n);if(i){i=r.fallback,o=t.mode,l=e.child,a=l.sibling;var c={mode:"hidden",children:r.children};return!(o&1)&&t.child!==l?(r=t.child,r.childLanes=0,r.pendingProps=c,t.deletions=null):(r=Ct(l,c),r.subtreeFlags=l.subtreeFlags&14680064),a!==null?i=Ct(a,i):(i=qt(i,o,n,null),i.flags|=2),i.return=t,r.return=t,r.sibling=i,t.child=r,r=i,i=t.child,o=e.child.memoizedState,o=o===null?ai(n):{baseLanes:o.baseLanes|n,cachePool:null,transitions:o.transitions},i.memoizedState=o,i.childLanes=e.childLanes&~n,t.memoizedState=oi,r}return i=e.child,e=i.sibling,r=Ct(i,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function io(e,t){return t=Qs({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Fr(e,t,n,r){return r!==null&&Qi(r),Ln(t,e.child,null,n),e=io(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function gf(e,t,n,r,l,i,o){if(n)return t.flags&256?(t.flags&=-257,r=vl(Error(b(422))),Fr(e,t,o,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(i=r.fallback,l=t.mode,r=Qs({mode:"visible",children:r.children},l,0,null),i=qt(i,l,o,null),i.flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,t.mode&1&&Ln(t,e.child,null,o),t.child.memoizedState=ai(o),t.memoizedState=oi,i);if(!(t.mode&1))return Fr(e,t,o,null);if(l.data==="$!"){if(r=l.nextSibling&&l.nextSibling.dataset,r)var a=r.dgst;return r=a,i=Error(b(419)),r=vl(i,r,void 0),Fr(e,t,o,r)}if(a=(o&e.childLanes)!==0,Se||a){if(r=le,r!==null){switch(o&-o){case 4:l=2;break;case 16:l=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:l=32;break;case 536870912:l=268435456;break;default:l=0}l=l&(r.suspendedLanes|o)?0:l,l!==0&&l!==i.retryLane&&(i.retryLane=l,at(e,l),Qe(r,e,l,-1))}return fo(),r=vl(Error(b(421))),Fr(e,t,o,r)}return l.data==="$?"?(t.flags|=128,t.child=e.child,t=Df.bind(null,e),l._reactRetry=t,null):(e=i.treeContext,De=Nt(l.nextSibling),Me=t,G=!0,He=null,e!==null&&(Pe[_e++]=nt,Pe[_e++]=rt,Pe[_e++]=Xt,nt=e.id,rt=e.overflow,Xt=t),t=io(t,r.children),t.flags|=4096,t)}function ja(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),ti(e.return,t,n)}function jl(e,t,n,r,l){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:l}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=l)}function Au(e,t,n){var r=t.pendingProps,l=r.revealOrder,i=r.tail;if(xe(e,t,r.children,n),r=q.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&ja(e,n,t);else if(e.tag===19)ja(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(B(q,r),!(t.mode&1))t.memoizedState=null;else switch(l){case"forwards":for(n=t.child,l=null;n!==null;)e=n.alternate,e!==null&&ws(e)===null&&(l=n),n=n.sibling;n=l,n===null?(l=t.child,t.child=null):(l=n.sibling,n.sibling=null),jl(t,!1,l,n,i);break;case"backwards":for(n=null,l=t.child,t.child=null;l!==null;){if(e=l.alternate,e!==null&&ws(e)===null){t.child=l;break}e=l.sibling,l.sibling=n,n=l,l=e}jl(t,!0,n,null,i);break;case"together":jl(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function es(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function ct(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Jt|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(b(153));if(t.child!==null){for(e=t.child,n=Ct(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Ct(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function xf(e,t,n){switch(t.tag){case 3:Mu(t),En();break;case 5:su(t);break;case 1:be(t.type)&&hs(t);break;case 4:Zi(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,l=t.memoizedProps.value;B(ys,r._currentValue),r._currentValue=l;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(B(q,q.current&1),t.flags|=128,null):n&t.child.childLanes?Tu(e,t,n):(B(q,q.current&1),e=ct(e,t,n),e!==null?e.sibling:null);B(q,q.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return Au(e,t,n);t.flags|=128}if(l=t.memoizedState,l!==null&&(l.rendering=null,l.tail=null,l.lastEffect=null),B(q,q.current),r)break;return null;case 22:case 23:return t.lanes=0,Lu(e,t,n)}return ct(e,t,n)}var Iu,ci,Pu,_u;Iu=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};ci=function(){};Pu=function(e,t,n,r){var l=e.memoizedProps;if(l!==r){e=t.stateNode,Kt(Ze.current);var i=null;switch(n){case"input":l=Tl(e,l),r=Tl(e,r),i=[];break;case"select":l=X({},l,{value:void 0}),r=X({},r,{value:void 0}),i=[];break;case"textarea":l=Pl(e,l),r=Pl(e,r),i=[];break;default:typeof l.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=fs)}zl(n,r);var o;n=null;for(f in l)if(!r.hasOwnProperty(f)&&l.hasOwnProperty(f)&&l[f]!=null)if(f==="style"){var a=l[f];for(o in a)a.hasOwnProperty(o)&&(n||(n={}),n[o]="")}else f!=="dangerouslySetInnerHTML"&&f!=="children"&&f!=="suppressContentEditableWarning"&&f!=="suppressHydrationWarning"&&f!=="autoFocus"&&(rr.hasOwnProperty(f)?i||(i=[]):(i=i||[]).push(f,null));for(f in r){var c=r[f];if(a=l!=null?l[f]:void 0,r.hasOwnProperty(f)&&c!==a&&(c!=null||a!=null))if(f==="style")if(a){for(o in a)!a.hasOwnProperty(o)||c&&c.hasOwnProperty(o)||(n||(n={}),n[o]="");for(o in c)c.hasOwnProperty(o)&&a[o]!==c[o]&&(n||(n={}),n[o]=c[o])}else n||(i||(i=[]),i.push(f,n)),n=c;else f==="dangerouslySetInnerHTML"?(c=c?c.__html:void 0,a=a?a.__html:void 0,c!=null&&a!==c&&(i=i||[]).push(f,c)):f==="children"?typeof c!="string"&&typeof c!="number"||(i=i||[]).push(f,""+c):f!=="suppressContentEditableWarning"&&f!=="suppressHydrationWarning"&&(rr.hasOwnProperty(f)?(c!=null&&f==="onScroll"&&W("scroll",e),i||a===c||(i=[])):(i=i||[]).push(f,c))}n&&(i=i||[]).push("style",n);var f=i;(t.updateQueue=f)&&(t.flags|=4)}};_u=function(e,t,n,r){n!==r&&(t.flags|=4)};function Vn(e,t){if(!G)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function fe(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var l=e.child;l!==null;)n|=l.lanes|l.childLanes,r|=l.subtreeFlags&14680064,r|=l.flags&14680064,l.return=e,l=l.sibling;else for(l=e.child;l!==null;)n|=l.lanes|l.childLanes,r|=l.subtreeFlags,r|=l.flags,l.return=e,l=l.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function yf(e,t,n){var r=t.pendingProps;switch(Wi(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return fe(t),null;case 1:return be(t.type)&&ps(),fe(t),null;case 3:return r=t.stateNode,Dn(),Q(ke),Q(he),eo(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Or(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,He!==null&&(xi(He),He=null))),ci(e,t),fe(t),null;case 5:Ji(t);var l=Kt(hr.current);if(n=t.type,e!==null&&t.stateNode!=null)Pu(e,t,n,r,l),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(b(166));return fe(t),null}if(e=Kt(Ze.current),Or(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[Ye]=t,r[fr]=i,e=(t.mode&1)!==0,n){case"dialog":W("cancel",r),W("close",r);break;case"iframe":case"object":case"embed":W("load",r);break;case"video":case"audio":for(l=0;l<Kn.length;l++)W(Kn[l],r);break;case"source":W("error",r);break;case"img":case"image":case"link":W("error",r),W("load",r);break;case"details":W("toggle",r);break;case"input":Do(r,i),W("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},W("invalid",r);break;case"textarea":To(r,i),W("invalid",r)}zl(n,i),l=null;for(var o in i)if(i.hasOwnProperty(o)){var a=i[o];o==="children"?typeof a=="string"?r.textContent!==a&&(i.suppressHydrationWarning!==!0&&$r(r.textContent,a,e),l=["children",a]):typeof a=="number"&&r.textContent!==""+a&&(i.suppressHydrationWarning!==!0&&$r(r.textContent,a,e),l=["children",""+a]):rr.hasOwnProperty(o)&&a!=null&&o==="onScroll"&&W("scroll",r)}switch(n){case"input":Mr(r),Mo(r,i,!0);break;case"textarea":Mr(r),Ao(r);break;case"select":case"option":break;default:typeof i.onClick=="function"&&(r.onclick=fs)}r=l,t.updateQueue=r,r!==null&&(t.flags|=4)}else{o=l.nodeType===9?l:l.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=cc(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=o.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=o.createElement(n,{is:r.is}):(e=o.createElement(n),n==="select"&&(o=e,r.multiple?o.multiple=!0:r.size&&(o.size=r.size))):e=o.createElementNS(e,n),e[Ye]=t,e[fr]=r,Iu(e,t,!1,!1),t.stateNode=e;e:{switch(o=Rl(n,r),n){case"dialog":W("cancel",e),W("close",e),l=r;break;case"iframe":case"object":case"embed":W("load",e),l=r;break;case"video":case"audio":for(l=0;l<Kn.length;l++)W(Kn[l],e);l=r;break;case"source":W("error",e),l=r;break;case"img":case"image":case"link":W("error",e),W("load",e),l=r;break;case"details":W("toggle",e),l=r;break;case"input":Do(e,r),l=Tl(e,r),W("invalid",e);break;case"option":l=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},l=X({},r,{value:void 0}),W("invalid",e);break;case"textarea":To(e,r),l=Pl(e,r),W("invalid",e);break;default:l=r}zl(n,l),a=l;for(i in a)if(a.hasOwnProperty(i)){var c=a[i];i==="style"?mc(e,c):i==="dangerouslySetInnerHTML"?(c=c?c.__html:void 0,c!=null&&uc(e,c)):i==="children"?typeof c=="string"?(n!=="textarea"||c!=="")&&sr(e,c):typeof c=="number"&&sr(e,""+c):i!=="suppressContentEditableWarning"&&i!=="suppressHydrationWarning"&&i!=="autoFocus"&&(rr.hasOwnProperty(i)?c!=null&&i==="onScroll"&&W("scroll",e):c!=null&&Mi(e,i,c,o))}switch(n){case"input":Mr(e),Mo(e,r,!1);break;case"textarea":Mr(e),Ao(e);break;case"option":r.value!=null&&e.setAttribute("value",""+Dt(r.value));break;case"select":e.multiple=!!r.multiple,i=r.value,i!=null?vn(e,!!r.multiple,i,!1):r.defaultValue!=null&&vn(e,!!r.multiple,r.defaultValue,!0);break;default:typeof l.onClick=="function"&&(e.onclick=fs)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return fe(t),null;case 6:if(e&&t.stateNode!=null)_u(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(b(166));if(n=Kt(hr.current),Kt(Ze.current),Or(t)){if(r=t.stateNode,n=t.memoizedProps,r[Ye]=t,(i=r.nodeValue!==n)&&(e=Me,e!==null))switch(e.tag){case 3:$r(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&$r(r.nodeValue,n,(e.mode&1)!==0)}i&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[Ye]=t,t.stateNode=r}return fe(t),null;case 13:if(Q(q),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(G&&De!==null&&t.mode&1&&!(t.flags&128))Jc(),En(),t.flags|=98560,i=!1;else if(i=Or(t),r!==null&&r.dehydrated!==null){if(e===null){if(!i)throw Error(b(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(b(317));i[Ye]=t}else En(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;fe(t),i=!1}else He!==null&&(xi(He),He=null),i=!0;if(!i)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||q.current&1?re===0&&(re=3):fo())),t.updateQueue!==null&&(t.flags|=4),fe(t),null);case 4:return Dn(),ci(e,t),e===null&&dr(t.stateNode.containerInfo),fe(t),null;case 10:return qi(t.type._context),fe(t),null;case 17:return be(t.type)&&ps(),fe(t),null;case 19:if(Q(q),i=t.memoizedState,i===null)return fe(t),null;if(r=(t.flags&128)!==0,o=i.rendering,o===null)if(r)Vn(i,!1);else{if(re!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(o=ws(e),o!==null){for(t.flags|=128,Vn(i,!1),r=o.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)i=n,e=r,i.flags&=14680066,o=i.alternate,o===null?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=o.childLanes,i.lanes=o.lanes,i.child=o.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=o.memoizedProps,i.memoizedState=o.memoizedState,i.updateQueue=o.updateQueue,i.type=o.type,e=o.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return B(q,q.current&1|2),t.child}e=e.sibling}i.tail!==null&&J()>Tn&&(t.flags|=128,r=!0,Vn(i,!1),t.lanes=4194304)}else{if(!r)if(e=ws(o),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),Vn(i,!0),i.tail===null&&i.tailMode==="hidden"&&!o.alternate&&!G)return fe(t),null}else 2*J()-i.renderingStartTime>Tn&&n!==1073741824&&(t.flags|=128,r=!0,Vn(i,!1),t.lanes=4194304);i.isBackwards?(o.sibling=t.child,t.child=o):(n=i.last,n!==null?n.sibling=o:t.child=o,i.last=o)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=J(),t.sibling=null,n=q.current,B(q,r?n&1|2:n&1),t):(fe(t),null);case 22:case 23:return mo(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Le&1073741824&&(fe(t),t.subtreeFlags&6&&(t.flags|=8192)):fe(t),null;case 24:return null;case 25:return null}throw Error(b(156,t.tag))}function vf(e,t){switch(Wi(t),t.tag){case 1:return be(t.type)&&ps(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Dn(),Q(ke),Q(he),eo(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Ji(t),null;case 13:if(Q(q),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(b(340));En()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return Q(q),null;case 4:return Dn(),null;case 10:return qi(t.type._context),null;case 22:case 23:return mo(),null;case 24:return null;default:return null}}var Vr=!1,pe=!1,jf=typeof WeakSet=="function"?WeakSet:Set,L=null;function xn(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){Z(e,t,r)}else n.current=null}function ui(e,t,n){try{n()}catch(r){Z(e,t,r)}}var wa=!1;function wf(e,t){if(Kl=us,e=Uc(),Hi(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var l=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch{n=null;break e}var o=0,a=-1,c=-1,f=0,x=0,g=e,y=null;t:for(;;){for(var v;g!==n||l!==0&&g.nodeType!==3||(a=o+l),g!==i||r!==0&&g.nodeType!==3||(c=o+r),g.nodeType===3&&(o+=g.nodeValue.length),(v=g.firstChild)!==null;)y=g,g=v;for(;;){if(g===e)break t;if(y===n&&++f===l&&(a=o),y===i&&++x===r&&(c=o),(v=g.nextSibling)!==null)break;g=y,y=g.parentNode}g=v}n=a===-1||c===-1?null:{start:a,end:c}}else n=null}n=n||{start:0,end:0}}else n=null;for(Gl={focusedElem:e,selectionRange:n},us=!1,L=t;L!==null;)if(t=L,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,L=e;else for(;L!==null;){t=L;try{var N=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(N!==null){var h=N.memoizedProps,S=N.memoizedState,m=t.stateNode,d=m.getSnapshotBeforeUpdate(t.elementType===t.type?h:Fe(t.type,h),S);m.__reactInternalSnapshotBeforeUpdate=d}break;case 3:var u=t.stateNode.containerInfo;u.nodeType===1?u.textContent="":u.nodeType===9&&u.documentElement&&u.removeChild(u.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(b(163))}}catch(p){Z(t,t.return,p)}if(e=t.sibling,e!==null){e.return=t.return,L=e;break}L=t.return}return N=wa,wa=!1,N}function er(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var l=r=r.next;do{if((l.tag&e)===e){var i=l.destroy;l.destroy=void 0,i!==void 0&&ui(t,n,i)}l=l.next}while(l!==r)}}function Bs(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function di(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function zu(e){var t=e.alternate;t!==null&&(e.alternate=null,zu(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[Ye],delete t[fr],delete t[Xl],delete t[nf],delete t[rf])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Ru(e){return e.tag===5||e.tag===3||e.tag===4}function Na(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Ru(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function mi(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=fs));else if(r!==4&&(e=e.child,e!==null))for(mi(e,t,n),e=e.sibling;e!==null;)mi(e,t,n),e=e.sibling}function fi(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(fi(e,t,n),e=e.sibling;e!==null;)fi(e,t,n),e=e.sibling}var ce=null,Ve=!1;function mt(e,t,n){for(n=n.child;n!==null;)$u(e,t,n),n=n.sibling}function $u(e,t,n){if(Xe&&typeof Xe.onCommitFiberUnmount=="function")try{Xe.onCommitFiberUnmount(zs,n)}catch{}switch(n.tag){case 5:pe||xn(n,t);case 6:var r=ce,l=Ve;ce=null,mt(e,t,n),ce=r,Ve=l,ce!==null&&(Ve?(e=ce,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):ce.removeChild(n.stateNode));break;case 18:ce!==null&&(Ve?(e=ce,n=n.stateNode,e.nodeType===8?fl(e.parentNode,n):e.nodeType===1&&fl(e,n),ar(e)):fl(ce,n.stateNode));break;case 4:r=ce,l=Ve,ce=n.stateNode.containerInfo,Ve=!0,mt(e,t,n),ce=r,Ve=l;break;case 0:case 11:case 14:case 15:if(!pe&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){l=r=r.next;do{var i=l,o=i.destroy;i=i.tag,o!==void 0&&(i&2||i&4)&&ui(n,t,o),l=l.next}while(l!==r)}mt(e,t,n);break;case 1:if(!pe&&(xn(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(a){Z(n,t,a)}mt(e,t,n);break;case 21:mt(e,t,n);break;case 22:n.mode&1?(pe=(r=pe)||n.memoizedState!==null,mt(e,t,n),pe=r):mt(e,t,n);break;default:mt(e,t,n)}}function Sa(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new jf),t.forEach(function(r){var l=Mf.bind(null,e,r);n.has(r)||(n.add(r),r.then(l,l))})}}function Ue(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var l=n[r];try{var i=e,o=t,a=o;e:for(;a!==null;){switch(a.tag){case 5:ce=a.stateNode,Ve=!1;break e;case 3:ce=a.stateNode.containerInfo,Ve=!0;break e;case 4:ce=a.stateNode.containerInfo,Ve=!0;break e}a=a.return}if(ce===null)throw Error(b(160));$u(i,o,l),ce=null,Ve=!1;var c=l.alternate;c!==null&&(c.return=null),l.return=null}catch(f){Z(l,t,f)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)Ou(t,e),t=t.sibling}function Ou(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Ue(t,e),Ge(e),r&4){try{er(3,e,e.return),Bs(3,e)}catch(h){Z(e,e.return,h)}try{er(5,e,e.return)}catch(h){Z(e,e.return,h)}}break;case 1:Ue(t,e),Ge(e),r&512&&n!==null&&xn(n,n.return);break;case 5:if(Ue(t,e),Ge(e),r&512&&n!==null&&xn(n,n.return),e.flags&32){var l=e.stateNode;try{sr(l,"")}catch(h){Z(e,e.return,h)}}if(r&4&&(l=e.stateNode,l!=null)){var i=e.memoizedProps,o=n!==null?n.memoizedProps:i,a=e.type,c=e.updateQueue;if(e.updateQueue=null,c!==null)try{a==="input"&&i.type==="radio"&&i.name!=null&&oc(l,i),Rl(a,o);var f=Rl(a,i);for(o=0;o<c.length;o+=2){var x=c[o],g=c[o+1];x==="style"?mc(l,g):x==="dangerouslySetInnerHTML"?uc(l,g):x==="children"?sr(l,g):Mi(l,x,g,f)}switch(a){case"input":Al(l,i);break;case"textarea":ac(l,i);break;case"select":var y=l._wrapperState.wasMultiple;l._wrapperState.wasMultiple=!!i.multiple;var v=i.value;v!=null?vn(l,!!i.multiple,v,!1):y!==!!i.multiple&&(i.defaultValue!=null?vn(l,!!i.multiple,i.defaultValue,!0):vn(l,!!i.multiple,i.multiple?[]:"",!1))}l[fr]=i}catch(h){Z(e,e.return,h)}}break;case 6:if(Ue(t,e),Ge(e),r&4){if(e.stateNode===null)throw Error(b(162));l=e.stateNode,i=e.memoizedProps;try{l.nodeValue=i}catch(h){Z(e,e.return,h)}}break;case 3:if(Ue(t,e),Ge(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{ar(t.containerInfo)}catch(h){Z(e,e.return,h)}break;case 4:Ue(t,e),Ge(e);break;case 13:Ue(t,e),Ge(e),l=e.child,l.flags&8192&&(i=l.memoizedState!==null,l.stateNode.isHidden=i,!i||l.alternate!==null&&l.alternate.memoizedState!==null||(co=J())),r&4&&Sa(e);break;case 22:if(x=n!==null&&n.memoizedState!==null,e.mode&1?(pe=(f=pe)||x,Ue(t,e),pe=f):Ue(t,e),Ge(e),r&8192){if(f=e.memoizedState!==null,(e.stateNode.isHidden=f)&&!x&&e.mode&1)for(L=e,x=e.child;x!==null;){for(g=L=x;L!==null;){switch(y=L,v=y.child,y.tag){case 0:case 11:case 14:case 15:er(4,y,y.return);break;case 1:xn(y,y.return);var N=y.stateNode;if(typeof N.componentWillUnmount=="function"){r=y,n=y.return;try{t=r,N.props=t.memoizedProps,N.state=t.memoizedState,N.componentWillUnmount()}catch(h){Z(r,n,h)}}break;case 5:xn(y,y.return);break;case 22:if(y.memoizedState!==null){ba(g);continue}}v!==null?(v.return=y,L=v):ba(g)}x=x.sibling}e:for(x=null,g=e;;){if(g.tag===5){if(x===null){x=g;try{l=g.stateNode,f?(i=l.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none"):(a=g.stateNode,c=g.memoizedProps.style,o=c!=null&&c.hasOwnProperty("display")?c.display:null,a.style.display=dc("display",o))}catch(h){Z(e,e.return,h)}}}else if(g.tag===6){if(x===null)try{g.stateNode.nodeValue=f?"":g.memoizedProps}catch(h){Z(e,e.return,h)}}else if((g.tag!==22&&g.tag!==23||g.memoizedState===null||g===e)&&g.child!==null){g.child.return=g,g=g.child;continue}if(g===e)break e;for(;g.sibling===null;){if(g.return===null||g.return===e)break e;x===g&&(x=null),g=g.return}x===g&&(x=null),g.sibling.return=g.return,g=g.sibling}}break;case 19:Ue(t,e),Ge(e),r&4&&Sa(e);break;case 21:break;default:Ue(t,e),Ge(e)}}function Ge(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(Ru(n)){var r=n;break e}n=n.return}throw Error(b(160))}switch(r.tag){case 5:var l=r.stateNode;r.flags&32&&(sr(l,""),r.flags&=-33);var i=Na(e);fi(e,i,l);break;case 3:case 4:var o=r.stateNode.containerInfo,a=Na(e);mi(e,a,o);break;default:throw Error(b(161))}}catch(c){Z(e,e.return,c)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Nf(e,t,n){L=e,Uu(e)}function Uu(e,t,n){for(var r=(e.mode&1)!==0;L!==null;){var l=L,i=l.child;if(l.tag===22&&r){var o=l.memoizedState!==null||Vr;if(!o){var a=l.alternate,c=a!==null&&a.memoizedState!==null||pe;a=Vr;var f=pe;if(Vr=o,(pe=c)&&!f)for(L=l;L!==null;)o=L,c=o.child,o.tag===22&&o.memoizedState!==null?Ca(l):c!==null?(c.return=o,L=c):Ca(l);for(;i!==null;)L=i,Uu(i),i=i.sibling;L=l,Vr=a,pe=f}ka(e)}else l.subtreeFlags&8772&&i!==null?(i.return=l,L=i):ka(e)}}function ka(e){for(;L!==null;){var t=L;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:pe||Bs(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!pe)if(n===null)r.componentDidMount();else{var l=t.elementType===t.type?n.memoizedProps:Fe(t.type,n.memoizedProps);r.componentDidUpdate(l,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;i!==null&&aa(t,i,r);break;case 3:var o=t.updateQueue;if(o!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}aa(t,o,n)}break;case 5:var a=t.stateNode;if(n===null&&t.flags&4){n=a;var c=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":c.autoFocus&&n.focus();break;case"img":c.src&&(n.src=c.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var f=t.alternate;if(f!==null){var x=f.memoizedState;if(x!==null){var g=x.dehydrated;g!==null&&ar(g)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(b(163))}pe||t.flags&512&&di(t)}catch(y){Z(t,t.return,y)}}if(t===e){L=null;break}if(n=t.sibling,n!==null){n.return=t.return,L=n;break}L=t.return}}function ba(e){for(;L!==null;){var t=L;if(t===e){L=null;break}var n=t.sibling;if(n!==null){n.return=t.return,L=n;break}L=t.return}}function Ca(e){for(;L!==null;){var t=L;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Bs(4,t)}catch(c){Z(t,n,c)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var l=t.return;try{r.componentDidMount()}catch(c){Z(t,l,c)}}var i=t.return;try{di(t)}catch(c){Z(t,i,c)}break;case 5:var o=t.return;try{di(t)}catch(c){Z(t,o,c)}}}catch(c){Z(t,t.return,c)}if(t===e){L=null;break}var a=t.sibling;if(a!==null){a.return=t.return,L=a;break}L=t.return}}var Sf=Math.ceil,ks=ut.ReactCurrentDispatcher,oo=ut.ReactCurrentOwner,Re=ut.ReactCurrentBatchConfig,$=0,le=null,te=null,ue=0,Le=0,yn=At(0),re=0,vr=null,Jt=0,Ws=0,ao=0,tr=null,Ne=null,co=0,Tn=1/0,Je=null,bs=!1,pi=null,kt=null,Hr=!1,yt=null,Cs=0,nr=0,hi=null,ts=-1,ns=0;function ye(){return $&6?J():ts!==-1?ts:ts=J()}function bt(e){return e.mode&1?$&2&&ue!==0?ue&-ue:lf.transition!==null?(ns===0&&(ns=kc()),ns):(e=F,e!==0||(e=window.event,e=e===void 0?16:Tc(e.type)),e):1}function Qe(e,t,n,r){if(50<nr)throw nr=0,hi=null,Error(b(185));Nr(e,n,r),(!($&2)||e!==le)&&(e===le&&(!($&2)&&(Ws|=n),re===4&&gt(e,ue)),Ce(e,r),n===1&&$===0&&!(t.mode&1)&&(Tn=J()+500,Fs&&It()))}function Ce(e,t){var n=e.callbackNode;lm(e,t);var r=cs(e,e===le?ue:0);if(r===0)n!==null&&_o(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&_o(n),t===1)e.tag===0?sf(Ea.bind(null,e)):Yc(Ea.bind(null,e)),ef(function(){!($&6)&&It()}),n=null;else{switch(bc(r)){case 1:n=_i;break;case 4:n=Nc;break;case 16:n=as;break;case 536870912:n=Sc;break;default:n=as}n=Gu(n,Fu.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function Fu(e,t){if(ts=-1,ns=0,$&6)throw Error(b(327));var n=e.callbackNode;if(kn()&&e.callbackNode!==n)return null;var r=cs(e,e===le?ue:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Es(e,r);else{t=r;var l=$;$|=2;var i=Hu();(le!==e||ue!==t)&&(Je=null,Tn=J()+500,Gt(e,t));do try{Cf();break}catch(a){Vu(e,a)}while(1);Gi(),ks.current=i,$=l,te!==null?t=0:(le=null,ue=0,t=re)}if(t!==0){if(t===2&&(l=Vl(e),l!==0&&(r=l,t=gi(e,l))),t===1)throw n=vr,Gt(e,0),gt(e,r),Ce(e,J()),n;if(t===6)gt(e,r);else{if(l=e.current.alternate,!(r&30)&&!kf(l)&&(t=Es(e,r),t===2&&(i=Vl(e),i!==0&&(r=i,t=gi(e,i))),t===1))throw n=vr,Gt(e,0),gt(e,r),Ce(e,J()),n;switch(e.finishedWork=l,e.finishedLanes=r,t){case 0:case 1:throw Error(b(345));case 2:Ut(e,Ne,Je);break;case 3:if(gt(e,r),(r&130023424)===r&&(t=co+500-J(),10<t)){if(cs(e,0)!==0)break;if(l=e.suspendedLanes,(l&r)!==r){ye(),e.pingedLanes|=e.suspendedLanes&l;break}e.timeoutHandle=Yl(Ut.bind(null,e,Ne,Je),t);break}Ut(e,Ne,Je);break;case 4:if(gt(e,r),(r&4194240)===r)break;for(t=e.eventTimes,l=-1;0<r;){var o=31-We(r);i=1<<o,o=t[o],o>l&&(l=o),r&=~i}if(r=l,r=J()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Sf(r/1960))-r,10<r){e.timeoutHandle=Yl(Ut.bind(null,e,Ne,Je),r);break}Ut(e,Ne,Je);break;case 5:Ut(e,Ne,Je);break;default:throw Error(b(329))}}}return Ce(e,J()),e.callbackNode===n?Fu.bind(null,e):null}function gi(e,t){var n=tr;return e.current.memoizedState.isDehydrated&&(Gt(e,t).flags|=256),e=Es(e,t),e!==2&&(t=Ne,Ne=n,t!==null&&xi(t)),e}function xi(e){Ne===null?Ne=e:Ne.push.apply(Ne,e)}function kf(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var l=n[r],i=l.getSnapshot;l=l.value;try{if(!Ke(i(),l))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function gt(e,t){for(t&=~ao,t&=~Ws,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-We(t),r=1<<n;e[n]=-1,t&=~r}}function Ea(e){if($&6)throw Error(b(327));kn();var t=cs(e,0);if(!(t&1))return Ce(e,J()),null;var n=Es(e,t);if(e.tag!==0&&n===2){var r=Vl(e);r!==0&&(t=r,n=gi(e,r))}if(n===1)throw n=vr,Gt(e,0),gt(e,t),Ce(e,J()),n;if(n===6)throw Error(b(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Ut(e,Ne,Je),Ce(e,J()),null}function uo(e,t){var n=$;$|=1;try{return e(t)}finally{$=n,$===0&&(Tn=J()+500,Fs&&It())}}function en(e){yt!==null&&yt.tag===0&&!($&6)&&kn();var t=$;$|=1;var n=Re.transition,r=F;try{if(Re.transition=null,F=1,e)return e()}finally{F=r,Re.transition=n,$=t,!($&6)&&It()}}function mo(){Le=yn.current,Q(yn)}function Gt(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,Jm(n)),te!==null)for(n=te.return;n!==null;){var r=n;switch(Wi(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&ps();break;case 3:Dn(),Q(ke),Q(he),eo();break;case 5:Ji(r);break;case 4:Dn();break;case 13:Q(q);break;case 19:Q(q);break;case 10:qi(r.type._context);break;case 22:case 23:mo()}n=n.return}if(le=e,te=e=Ct(e.current,null),ue=Le=t,re=0,vr=null,ao=Ws=Jt=0,Ne=tr=null,Qt!==null){for(t=0;t<Qt.length;t++)if(n=Qt[t],r=n.interleaved,r!==null){n.interleaved=null;var l=r.next,i=n.pending;if(i!==null){var o=i.next;i.next=l,r.next=o}n.pending=r}Qt=null}return e}function Vu(e,t){do{var n=te;try{if(Gi(),Zr.current=Ss,Ns){for(var r=Y.memoizedState;r!==null;){var l=r.queue;l!==null&&(l.pending=null),r=r.next}Ns=!1}if(Zt=0,se=ne=Y=null,Jn=!1,gr=0,oo.current=null,n===null||n.return===null){re=1,vr=t,te=null;break}e:{var i=e,o=n.return,a=n,c=t;if(t=ue,a.flags|=32768,c!==null&&typeof c=="object"&&typeof c.then=="function"){var f=c,x=a,g=x.tag;if(!(x.mode&1)&&(g===0||g===11||g===15)){var y=x.alternate;y?(x.updateQueue=y.updateQueue,x.memoizedState=y.memoizedState,x.lanes=y.lanes):(x.updateQueue=null,x.memoizedState=null)}var v=pa(o);if(v!==null){v.flags&=-257,ha(v,o,a,i,t),v.mode&1&&fa(i,f,t),t=v,c=f;var N=t.updateQueue;if(N===null){var h=new Set;h.add(c),t.updateQueue=h}else N.add(c);break e}else{if(!(t&1)){fa(i,f,t),fo();break e}c=Error(b(426))}}else if(G&&a.mode&1){var S=pa(o);if(S!==null){!(S.flags&65536)&&(S.flags|=256),ha(S,o,a,i,t),Qi(Mn(c,a));break e}}i=c=Mn(c,a),re!==4&&(re=2),tr===null?tr=[i]:tr.push(i),i=o;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t;var m=bu(i,c,t);oa(i,m);break e;case 1:a=c;var d=i.type,u=i.stateNode;if(!(i.flags&128)&&(typeof d.getDerivedStateFromError=="function"||u!==null&&typeof u.componentDidCatch=="function"&&(kt===null||!kt.has(u)))){i.flags|=65536,t&=-t,i.lanes|=t;var p=Cu(i,a,t);oa(i,p);break e}}i=i.return}while(i!==null)}Wu(n)}catch(j){t=j,te===n&&n!==null&&(te=n=n.return);continue}break}while(1)}function Hu(){var e=ks.current;return ks.current=Ss,e===null?Ss:e}function fo(){(re===0||re===3||re===2)&&(re=4),le===null||!(Jt&268435455)&&!(Ws&268435455)||gt(le,ue)}function Es(e,t){var n=$;$|=2;var r=Hu();(le!==e||ue!==t)&&(Je=null,Gt(e,t));do try{bf();break}catch(l){Vu(e,l)}while(1);if(Gi(),$=n,ks.current=r,te!==null)throw Error(b(261));return le=null,ue=0,re}function bf(){for(;te!==null;)Bu(te)}function Cf(){for(;te!==null&&!Yd();)Bu(te)}function Bu(e){var t=Ku(e.alternate,e,Le);e.memoizedProps=e.pendingProps,t===null?Wu(e):te=t,oo.current=null}function Wu(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=vf(n,t),n!==null){n.flags&=32767,te=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{re=6,te=null;return}}else if(n=yf(n,t,Le),n!==null){te=n;return}if(t=t.sibling,t!==null){te=t;return}te=t=e}while(t!==null);re===0&&(re=5)}function Ut(e,t,n){var r=F,l=Re.transition;try{Re.transition=null,F=1,Ef(e,t,n,r)}finally{Re.transition=l,F=r}return null}function Ef(e,t,n,r){do kn();while(yt!==null);if($&6)throw Error(b(327));n=e.finishedWork;var l=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(b(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(im(e,i),e===le&&(te=le=null,ue=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Hr||(Hr=!0,Gu(as,function(){return kn(),null})),i=(n.flags&15990)!==0,n.subtreeFlags&15990||i){i=Re.transition,Re.transition=null;var o=F;F=1;var a=$;$|=4,oo.current=null,wf(e,n),Ou(n,e),Qm(Gl),us=!!Kl,Gl=Kl=null,e.current=n,Nf(n),Xd(),$=a,F=o,Re.transition=i}else e.current=n;if(Hr&&(Hr=!1,yt=e,Cs=l),i=e.pendingLanes,i===0&&(kt=null),em(n.stateNode),Ce(e,J()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)l=t[n],r(l.value,{componentStack:l.stack,digest:l.digest});if(bs)throw bs=!1,e=pi,pi=null,e;return Cs&1&&e.tag!==0&&kn(),i=e.pendingLanes,i&1?e===hi?nr++:(nr=0,hi=e):nr=0,It(),null}function kn(){if(yt!==null){var e=bc(Cs),t=Re.transition,n=F;try{if(Re.transition=null,F=16>e?16:e,yt===null)var r=!1;else{if(e=yt,yt=null,Cs=0,$&6)throw Error(b(331));var l=$;for($|=4,L=e.current;L!==null;){var i=L,o=i.child;if(L.flags&16){var a=i.deletions;if(a!==null){for(var c=0;c<a.length;c++){var f=a[c];for(L=f;L!==null;){var x=L;switch(x.tag){case 0:case 11:case 15:er(8,x,i)}var g=x.child;if(g!==null)g.return=x,L=g;else for(;L!==null;){x=L;var y=x.sibling,v=x.return;if(zu(x),x===f){L=null;break}if(y!==null){y.return=v,L=y;break}L=v}}}var N=i.alternate;if(N!==null){var h=N.child;if(h!==null){N.child=null;do{var S=h.sibling;h.sibling=null,h=S}while(h!==null)}}L=i}}if(i.subtreeFlags&2064&&o!==null)o.return=i,L=o;else e:for(;L!==null;){if(i=L,i.flags&2048)switch(i.tag){case 0:case 11:case 15:er(9,i,i.return)}var m=i.sibling;if(m!==null){m.return=i.return,L=m;break e}L=i.return}}var d=e.current;for(L=d;L!==null;){o=L;var u=o.child;if(o.subtreeFlags&2064&&u!==null)u.return=o,L=u;else e:for(o=d;L!==null;){if(a=L,a.flags&2048)try{switch(a.tag){case 0:case 11:case 15:Bs(9,a)}}catch(j){Z(a,a.return,j)}if(a===o){L=null;break e}var p=a.sibling;if(p!==null){p.return=a.return,L=p;break e}L=a.return}}if($=l,It(),Xe&&typeof Xe.onPostCommitFiberRoot=="function")try{Xe.onPostCommitFiberRoot(zs,e)}catch{}r=!0}return r}finally{F=n,Re.transition=t}}return!1}function La(e,t,n){t=Mn(n,t),t=bu(e,t,1),e=St(e,t,1),t=ye(),e!==null&&(Nr(e,1,t),Ce(e,t))}function Z(e,t,n){if(e.tag===3)La(e,e,n);else for(;t!==null;){if(t.tag===3){La(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(kt===null||!kt.has(r))){e=Mn(n,e),e=Cu(t,e,1),t=St(t,e,1),e=ye(),t!==null&&(Nr(t,1,e),Ce(t,e));break}}t=t.return}}function Lf(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=ye(),e.pingedLanes|=e.suspendedLanes&n,le===e&&(ue&n)===n&&(re===4||re===3&&(ue&130023424)===ue&&500>J()-co?Gt(e,0):ao|=n),Ce(e,t)}function Qu(e,t){t===0&&(e.mode&1?(t=Ir,Ir<<=1,!(Ir&130023424)&&(Ir=4194304)):t=1);var n=ye();e=at(e,t),e!==null&&(Nr(e,t,n),Ce(e,n))}function Df(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Qu(e,n)}function Mf(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,l=e.memoizedState;l!==null&&(n=l.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(b(314))}r!==null&&r.delete(t),Qu(e,n)}var Ku;Ku=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||ke.current)Se=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Se=!1,xf(e,t,n);Se=!!(e.flags&131072)}else Se=!1,G&&t.flags&1048576&&Xc(t,xs,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;es(e,t),e=t.pendingProps;var l=Cn(t,he.current);Sn(t,n),l=no(null,t,r,e,l,n);var i=ro();return t.flags|=1,typeof l=="object"&&l!==null&&typeof l.render=="function"&&l.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,be(r)?(i=!0,hs(t)):i=!1,t.memoizedState=l.state!==null&&l.state!==void 0?l.state:null,Xi(t),l.updater=Hs,t.stateNode=l,l._reactInternals=t,ri(t,r,e,n),t=ii(null,t,r,!0,i,n)):(t.tag=0,G&&i&&Bi(t),xe(null,t,l,n),t=t.child),t;case 16:r=t.elementType;e:{switch(es(e,t),e=t.pendingProps,l=r._init,r=l(r._payload),t.type=r,l=t.tag=Af(r),e=Fe(r,e),l){case 0:t=li(null,t,r,e,n);break e;case 1:t=ya(null,t,r,e,n);break e;case 11:t=ga(null,t,r,e,n);break e;case 14:t=xa(null,t,r,Fe(r.type,e),n);break e}throw Error(b(306,r,""))}return t;case 0:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:Fe(r,l),li(e,t,r,l,n);case 1:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:Fe(r,l),ya(e,t,r,l,n);case 3:e:{if(Mu(t),e===null)throw Error(b(387));r=t.pendingProps,i=t.memoizedState,l=i.element,ru(e,t),js(t,r,null,n);var o=t.memoizedState;if(r=o.element,i.isDehydrated)if(i={element:r,isDehydrated:!1,cache:o.cache,pendingSuspenseBoundaries:o.pendingSuspenseBoundaries,transitions:o.transitions},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){l=Mn(Error(b(423)),t),t=va(e,t,r,n,l);break e}else if(r!==l){l=Mn(Error(b(424)),t),t=va(e,t,r,n,l);break e}else for(De=Nt(t.stateNode.containerInfo.firstChild),Me=t,G=!0,He=null,n=tu(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(En(),r===l){t=ct(e,t,n);break e}xe(e,t,r,n)}t=t.child}return t;case 5:return su(t),e===null&&ei(t),r=t.type,l=t.pendingProps,i=e!==null?e.memoizedProps:null,o=l.children,ql(r,l)?o=null:i!==null&&ql(r,i)&&(t.flags|=32),Du(e,t),xe(e,t,o,n),t.child;case 6:return e===null&&ei(t),null;case 13:return Tu(e,t,n);case 4:return Zi(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=Ln(t,null,r,n):xe(e,t,r,n),t.child;case 11:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:Fe(r,l),ga(e,t,r,l,n);case 7:return xe(e,t,t.pendingProps,n),t.child;case 8:return xe(e,t,t.pendingProps.children,n),t.child;case 12:return xe(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,l=t.pendingProps,i=t.memoizedProps,o=l.value,B(ys,r._currentValue),r._currentValue=o,i!==null)if(Ke(i.value,o)){if(i.children===l.children&&!ke.current){t=ct(e,t,n);break e}}else for(i=t.child,i!==null&&(i.return=t);i!==null;){var a=i.dependencies;if(a!==null){o=i.child;for(var c=a.firstContext;c!==null;){if(c.context===r){if(i.tag===1){c=lt(-1,n&-n),c.tag=2;var f=i.updateQueue;if(f!==null){f=f.shared;var x=f.pending;x===null?c.next=c:(c.next=x.next,x.next=c),f.pending=c}}i.lanes|=n,c=i.alternate,c!==null&&(c.lanes|=n),ti(i.return,n,t),a.lanes|=n;break}c=c.next}}else if(i.tag===10)o=i.type===t.type?null:i.child;else if(i.tag===18){if(o=i.return,o===null)throw Error(b(341));o.lanes|=n,a=o.alternate,a!==null&&(a.lanes|=n),ti(o,n,t),o=i.sibling}else o=i.child;if(o!==null)o.return=i;else for(o=i;o!==null;){if(o===t){o=null;break}if(i=o.sibling,i!==null){i.return=o.return,o=i;break}o=o.return}i=o}xe(e,t,l.children,n),t=t.child}return t;case 9:return l=t.type,r=t.pendingProps.children,Sn(t,n),l=$e(l),r=r(l),t.flags|=1,xe(e,t,r,n),t.child;case 14:return r=t.type,l=Fe(r,t.pendingProps),l=Fe(r.type,l),xa(e,t,r,l,n);case 15:return Eu(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:Fe(r,l),es(e,t),t.tag=1,be(r)?(e=!0,hs(t)):e=!1,Sn(t,n),ku(t,r,l),ri(t,r,l,n),ii(null,t,r,!0,e,n);case 19:return Au(e,t,n);case 22:return Lu(e,t,n)}throw Error(b(156,t.tag))};function Gu(e,t){return wc(e,t)}function Tf(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function ze(e,t,n,r){return new Tf(e,t,n,r)}function po(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Af(e){if(typeof e=="function")return po(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Ai)return 11;if(e===Ii)return 14}return 2}function Ct(e,t){var n=e.alternate;return n===null?(n=ze(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function rs(e,t,n,r,l,i){var o=2;if(r=e,typeof e=="function")po(e)&&(o=1);else if(typeof e=="string")o=5;else e:switch(e){case an:return qt(n.children,l,i,t);case Ti:o=8,l|=8;break;case El:return e=ze(12,n,t,l|2),e.elementType=El,e.lanes=i,e;case Ll:return e=ze(13,n,t,l),e.elementType=Ll,e.lanes=i,e;case Dl:return e=ze(19,n,t,l),e.elementType=Dl,e.lanes=i,e;case sc:return Qs(n,l,i,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case nc:o=10;break e;case rc:o=9;break e;case Ai:o=11;break e;case Ii:o=14;break e;case ft:o=16,r=null;break e}throw Error(b(130,e==null?e:typeof e,""))}return t=ze(o,n,t,l),t.elementType=e,t.type=r,t.lanes=i,t}function qt(e,t,n,r){return e=ze(7,e,r,t),e.lanes=n,e}function Qs(e,t,n,r){return e=ze(22,e,r,t),e.elementType=sc,e.lanes=n,e.stateNode={isHidden:!1},e}function wl(e,t,n){return e=ze(6,e,null,t),e.lanes=n,e}function Nl(e,t,n){return t=ze(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function If(e,t,n,r,l){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=nl(0),this.expirationTimes=nl(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=nl(0),this.identifierPrefix=r,this.onRecoverableError=l,this.mutableSourceEagerHydrationData=null}function ho(e,t,n,r,l,i,o,a,c){return e=new If(e,t,n,a,c),t===1?(t=1,i===!0&&(t|=8)):t=0,i=ze(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Xi(i),e}function Pf(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:on,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function qu(e){if(!e)return Mt;e=e._reactInternals;e:{if(nn(e)!==e||e.tag!==1)throw Error(b(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(be(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(b(171))}if(e.tag===1){var n=e.type;if(be(n))return qc(e,n,t)}return t}function Yu(e,t,n,r,l,i,o,a,c){return e=ho(n,r,!0,e,l,i,o,a,c),e.context=qu(null),n=e.current,r=ye(),l=bt(n),i=lt(r,l),i.callback=t??null,St(n,i,l),e.current.lanes=l,Nr(e,l,r),Ce(e,r),e}function Ks(e,t,n,r){var l=t.current,i=ye(),o=bt(l);return n=qu(n),t.context===null?t.context=n:t.pendingContext=n,t=lt(i,o),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=St(l,t,o),e!==null&&(Qe(e,l,o,i),Xr(e,l,o)),o}function Ls(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Da(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function go(e,t){Da(e,t),(e=e.alternate)&&Da(e,t)}function _f(){return null}var Xu=typeof reportError=="function"?reportError:function(e){console.error(e)};function xo(e){this._internalRoot=e}Gs.prototype.render=xo.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(b(409));Ks(e,t,null,null)};Gs.prototype.unmount=xo.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;en(function(){Ks(null,e,null,null)}),t[ot]=null}};function Gs(e){this._internalRoot=e}Gs.prototype.unstable_scheduleHydration=function(e){if(e){var t=Lc();e={blockedOn:null,target:e,priority:t};for(var n=0;n<ht.length&&t!==0&&t<ht[n].priority;n++);ht.splice(n,0,e),n===0&&Mc(e)}};function yo(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function qs(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Ma(){}function zf(e,t,n,r,l){if(l){if(typeof r=="function"){var i=r;r=function(){var f=Ls(o);i.call(f)}}var o=Yu(t,r,e,0,null,!1,!1,"",Ma);return e._reactRootContainer=o,e[ot]=o.current,dr(e.nodeType===8?e.parentNode:e),en(),o}for(;l=e.lastChild;)e.removeChild(l);if(typeof r=="function"){var a=r;r=function(){var f=Ls(c);a.call(f)}}var c=ho(e,0,!1,null,null,!1,!1,"",Ma);return e._reactRootContainer=c,e[ot]=c.current,dr(e.nodeType===8?e.parentNode:e),en(function(){Ks(t,c,n,r)}),c}function Ys(e,t,n,r,l){var i=n._reactRootContainer;if(i){var o=i;if(typeof l=="function"){var a=l;l=function(){var c=Ls(o);a.call(c)}}Ks(t,o,e,l)}else o=zf(n,t,e,l,r);return Ls(o)}Cc=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Qn(t.pendingLanes);n!==0&&(zi(t,n|1),Ce(t,J()),!($&6)&&(Tn=J()+500,It()))}break;case 13:en(function(){var r=at(e,1);if(r!==null){var l=ye();Qe(r,e,1,l)}}),go(e,1)}};Ri=function(e){if(e.tag===13){var t=at(e,134217728);if(t!==null){var n=ye();Qe(t,e,134217728,n)}go(e,134217728)}};Ec=function(e){if(e.tag===13){var t=bt(e),n=at(e,t);if(n!==null){var r=ye();Qe(n,e,t,r)}go(e,t)}};Lc=function(){return F};Dc=function(e,t){var n=F;try{return F=e,t()}finally{F=n}};Ol=function(e,t,n){switch(t){case"input":if(Al(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var l=Us(r);if(!l)throw Error(b(90));ic(r),Al(r,l)}}}break;case"textarea":ac(e,n);break;case"select":t=n.value,t!=null&&vn(e,!!n.multiple,t,!1)}};hc=uo;gc=en;var Rf={usingClientEntryPoint:!1,Events:[kr,mn,Us,fc,pc,uo]},Hn={findFiberByHostInstance:Wt,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},$f={bundleType:Hn.bundleType,version:Hn.version,rendererPackageName:Hn.rendererPackageName,rendererConfig:Hn.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:ut.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=vc(e),e===null?null:e.stateNode},findFiberByHostInstance:Hn.findFiberByHostInstance||_f,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Br=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Br.isDisabled&&Br.supportsFiber)try{zs=Br.inject($f),Xe=Br}catch{}}Ae.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Rf;Ae.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!yo(t))throw Error(b(200));return Pf(e,t,null,n)};Ae.createRoot=function(e,t){if(!yo(e))throw Error(b(299));var n=!1,r="",l=Xu;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(l=t.onRecoverableError)),t=ho(e,1,!1,null,null,n,!1,r,l),e[ot]=t.current,dr(e.nodeType===8?e.parentNode:e),new xo(t)};Ae.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(b(188)):(e=Object.keys(e).join(","),Error(b(268,e)));return e=vc(t),e=e===null?null:e.stateNode,e};Ae.flushSync=function(e){return en(e)};Ae.hydrate=function(e,t,n){if(!qs(t))throw Error(b(200));return Ys(null,e,t,!0,n)};Ae.hydrateRoot=function(e,t,n){if(!yo(e))throw Error(b(405));var r=n!=null&&n.hydratedSources||null,l=!1,i="",o=Xu;if(n!=null&&(n.unstable_strictMode===!0&&(l=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onRecoverableError!==void 0&&(o=n.onRecoverableError)),t=Yu(t,null,e,1,n??null,l,!1,i,o),e[ot]=t.current,dr(e),r)for(e=0;e<r.length;e++)n=r[e],l=n._getVersion,l=l(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,l]:t.mutableSourceEagerHydrationData.push(n,l);return new Gs(t)};Ae.render=function(e,t,n){if(!qs(t))throw Error(b(200));return Ys(null,e,t,!1,n)};Ae.unmountComponentAtNode=function(e){if(!qs(e))throw Error(b(40));return e._reactRootContainer?(en(function(){Ys(null,null,e,!1,function(){e._reactRootContainer=null,e[ot]=null})}),!0):!1};Ae.unstable_batchedUpdates=uo;Ae.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!qs(n))throw Error(b(200));if(e==null||e._reactInternals===void 0)throw Error(b(38));return Ys(e,t,n,!1,r)};Ae.version="18.3.1-next-f1338f8080-20240426";function Zu(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Zu)}catch(e){console.error(e)}}Zu(),Za.exports=Ae;var Of=Za.exports,Ta=Of;bl.createRoot=Ta.createRoot,bl.hydrateRoot=Ta.hydrateRoot;const Sl="http://localhost:5000/api";class Uf{constructor(t){ge(this,"baseURL");this.baseURL=t}async request(t,n={}){const r=`${this.baseURL}${t}`,l=new AbortController,i=setTimeout(()=>l.abort(),1e4),o={headers:{"Content-Type":"application/json",...n.headers},signal:l.signal,...n};try{const a=await fetch(r,o);if(clearTimeout(i),!a.ok){const c=await a.json().catch(()=>({}));throw new Error(c.error||`HTTP ${a.status}: ${a.statusText}`)}return await a.json()}catch(a){throw clearTimeout(i),console.error(`API request failed: ${r}`,a),a}}async get(t){return this.request(t,{method:"GET"})}async post(t,n){return this.request(t,{method:"POST",body:n?JSON.stringify(n):void 0})}async put(t,n){return this.request(t,{method:"PUT",body:n?JSON.stringify(n):void 0})}async delete(t){return this.request(t,{method:"DELETE"})}}class Ff{constructor(){ge(this,"client");ge(this,"isOnline",!0);ge(this,"connectionPromise",null);this.client=new Uf(Sl)}async checkConnection(){if(this.connectionPromise)return console.log("[ApiService] Connection check already in progress, reusing promise"),this.connectionPromise;this.connectionPromise=this._performConnectionCheck();try{return await this.connectionPromise}finally{this.connectionPromise=null}}async _performConnectionCheck(){try{const n=`${Sl.replace("/api","")}/health`;console.log(`[ApiService] Checking connection to: ${n}`);const r=new AbortController;let l=null;try{l=setTimeout(()=>{console.log("[ApiService] Connection check timeout, aborting request"),r.abort()},8e3);const i=await fetch(n,{signal:r.signal,method:"GET",cache:"no-cache",headers:{"Cache-Control":"no-cache"}});return l&&(clearTimeout(l),l=null),console.log(`[ApiService] Response status: ${i.status}`),i.ok?(this.isOnline=!0,console.log("[ApiService] Connection check successful"),!0):(this.isOnline=!1,console.warn(`[ApiService] Connection check failed with status: ${i.status}`),!1)}catch(i){throw l&&clearTimeout(l),i}}catch(t){return console.warn("[ApiService] Backend server is not available:",t),t instanceof Error&&(t.name==="AbortError"?console.warn("[ApiService] Request was aborted (likely due to timeout)"):t.name==="TypeError"&&t.message.includes("fetch")?console.warn("[ApiService] Network error or server not reachable"):console.warn(`[ApiService] Unexpected error: ${t.name} - ${t.message}`)),this.isOnline=!1,!1}}getConnectionStatus(){return this.isOnline}async getLLMConfigs(){return this.client.get("/llm-configs")}async createLLMConfig(t){return this.client.post("/llm-configs",t)}async updateLLMConfig(t,n){return this.client.put(`/llm-configs/${t}`,n)}async deleteLLMConfig(t){try{await this.client.delete(`/llm-configs/${t}`)}catch(n){throw n instanceof Error&&n.message.includes("无法删除正在使用的LLM配置")?new Error("无法删除正在使用的LLM配置，请先从智能体中移除此配置"):n}}async getAgents(){return this.client.get("/agents")}async createAgent(t){return this.client.post("/agents",t)}async updateAgent(t,n){return this.client.put(`/agents/${t}`,n)}async deleteAgent(t){await this.client.delete(`/agents/${t}`)}async getDiscussions(){return this.client.get("/discussions")}async createDiscussion(t){return this.client.post("/discussions",t)}async updateDiscussion(t,n){return this.client.put(`/discussions/${t}`,n)}async deleteDiscussion(t){await this.client.delete(`/discussions/${t}`)}async addMessage(t,n){return this.client.post(`/discussions/${t}/messages`,n)}async getSettings(){return this.client.get("/settings")}async updateSettings(t){return this.client.put("/settings",t)}async getPreferences(){return this.client.get("/preferences")}async updatePreferences(t){return this.client.put("/preferences",t)}async exportData(){return this.client.get("/data/export")}async importData(t,n=!1){await this.client.post("/data/import",{...t,clearExisting:n})}async clearAllData(){await this.client.delete("/data/clear")}async getStorageInfo(){return this.client.get("/storage/info")}}const V=new Ff,Vt=class Vt{constructor(){ge(this,"isInitialized",!1);ge(this,"storageMode","server");ge(this,"serverAvailable",!1)}static getInstance(){return Vt.instance||(Vt.instance=new Vt),Vt.instance}async initialize(){if(this.isInitialized)return;console.log("Starting StorageService initialization..."),console.log("Checking server connection..."),console.log("API_BASE_URL: http://localhost:5000/api");let t=0;const n=3;for(;t<n&&!this.serverAvailable;){t++,console.log(`[StorageService] Connection attempt ${t}/${n}`);try{if(this.serverAvailable=await V.checkConnection(),console.log(`[StorageService] Connection check result: ${this.serverAvailable}`),this.serverAvailable)break;t<n&&(console.log("[StorageService] Waiting 2 seconds before retry..."),await new Promise(r=>setTimeout(r,2e3)))}catch(r){console.error(`Server connection check failed (attempt ${t}):`,r),this.serverAvailable=!1,t<n&&(console.log("[StorageService] Waiting 2 seconds before retry..."),await new Promise(l=>setTimeout(l,2e3)))}}if(console.log(`Server available: ${this.serverAvailable}`),!this.serverAvailable)throw new Error("服务器连接失败，请确保后端服务正在运行");await this.determineStorageMode(),console.log(`Storage mode: ${this.storageMode}`),await this.initializeDefaultSettings(),console.log("Default settings initialized"),await this.migrateData(),console.log("Data migration completed"),this.isInitialized=!0,console.log(`StorageService initialized successfully with ${this.storageMode} mode`)}async determineStorageMode(){this.storageMode="server"}setStorageMode(t){this.storageMode=t}getStorageMode(){return this.storageMode}isServerAvailable(){return this.serverAvailable}async refreshServerConnection(){return this.serverAvailable=await V.checkConnection(),this.serverAvailable}async initializeDefaultSettings(){const t={version:"1.0.0",lastUpdated:new Date().toISOString(),autoSave:!0,maxStoredDiscussions:100,defaultDiscussionMode:"free",theme:"light"},n={defaultAgentCount:3,preferredLLMProvider:"openai",autoStartDiscussion:!1,showAdvancedOptions:!1,notificationsEnabled:!0,exportFormat:"json"};this.getSettings()||this.saveSettings(t),this.getPreferences()||this.savePreferences(n)}async migrateData(){const t=await this.getSettings();((t==null?void 0:t.version)||"0.0.0")<"1.0.0"&&console.log("Migrating data to version 1.0.0...")}async saveAgents(t){try{if(this.serverAvailable){console.log(`[StorageService] Starting batch save for ${t.length} agents`);const n=await this.getAgents(),r=t.map(async l=>n.findIndex(o=>o.id===l.id)>=0?V.updateAgent(l.id,l):V.createAgent(l));await Promise.all(r),console.log(`[StorageService] Successfully saved ${t.length} agents to server`)}else throw new Error("服务器不可用，无法保存智能体数据");this.updateLastModified()}catch(n){throw console.error("Failed to save agents:",n),new Error("Failed to save agents to storage")}}async getAgents(){if(this.serverAvailable)return await V.getAgents();throw new Error("服务器不可用，无法获取智能体数据")}async saveAgent(t){if(this.serverAvailable)(await this.getAgents()).findIndex(l=>l.id===t.id)>=0?await V.updateAgent(t.id,t):await V.createAgent(t);else throw new Error("服务器不可用，无法保存智能体")}async deleteAgent(t){if(this.serverAvailable)await V.deleteAgent(t);else throw new Error("服务器不可用，无法删除智能体")}async saveLLMConfigs(t){console.log(`[StorageService] Starting saveLLMConfigs with ${t.length} configs`),console.log(`[StorageService] Storage mode: ${this.storageMode}, Server available: ${this.serverAvailable}`);try{if(this.serverAvailable){console.log("[StorageService] Attempting to save LLM configs to server...");const n=await this.getLLMConfigs(),r=t.map(async l=>n.findIndex(o=>o.id===l.id)>=0?V.updateLLMConfig(l.id,l):V.createLLMConfig(l));await Promise.all(r),console.log(`[StorageService] Successfully saved ${t.length} LLM configs to server`)}else throw new Error("服务器不可用，无法保存LLM配置");this.updateLastModified(),console.log("[StorageService] saveLLMConfigs completed successfully")}catch(n){throw console.error("[StorageService] Failed to save LLM configs:",n),new Error("Failed to save LLM configs to storage")}}async getLLMConfigs(){if(this.serverAvailable){console.log("[StorageService] Using SERVER mode - attempting to load from server");const t=await V.getLLMConfigs();return console.log(`[StorageService] Successfully loaded ${t.length} configs from server`),t}else throw new Error("服务器不可用，无法获取LLM配置数据")}async saveLLMConfig(t){if(console.log(`[StorageService] Starting saveLLMConfig for config: ${t.id} (${t.name})`),console.log(`[StorageService] Storage mode: ${this.storageMode}, Server available: ${this.serverAvailable}`),this.serverAvailable){console.log("[StorageService] Attempting to save single config to server...");const r=(await this.getLLMConfigs()).findIndex(l=>l.id===t.id);console.log(`[StorageService] Existing config index: ${r}`),r>=0?(await V.updateLLMConfig(t.id,t),console.log("[StorageService] Config updated on server successfully")):(await V.createLLMConfig(t),console.log("[StorageService] Config created on server successfully")),console.log("[StorageService] saveLLMConfig completed successfully")}else throw new Error("服务器不可用，无法保存LLM配置")}async deleteLLMConfig(t){if(console.log(`[StorageService] Starting deleteLLMConfig for config: ${t}`),console.log(`[StorageService] Storage mode: ${this.storageMode}, Server available: ${this.serverAvailable}`),this.serverAvailable)console.log("[StorageService] Attempting to delete config from server..."),await V.deleteLLMConfig(t),console.log("[StorageService] Config deleted from server successfully"),console.log("[StorageService] deleteLLMConfig completed successfully");else throw new Error("服务器不可用，无法删除LLM配置")}async exportLLMConfigs(){try{const n=(await this.getLLMConfigs()).map(r=>({...r,apiKey:"***HIDDEN***"}));return JSON.stringify(n,null,2)}catch(t){throw console.error("Failed to export LLM configs:",t),new Error("导出LLM配置失败")}}async importLLMConfigs(t){const n={success:0,errors:[]};try{const r=JSON.parse(t);if(!Array.isArray(r))throw new Error("数据格式错误：应该是配置数组");for(const l of r)try{if(!l.id||!l.name||!l.provider||!l.model){n.errors.push(`配置 ${l.name||"Unknown"} 缺少必要字段`);continue}if(l.apiKey==="***HIDDEN***"){n.errors.push(`配置 ${l.name} 的API密钥需要重新设置`);continue}await this.saveLLMConfig(l),n.success++}catch(i){n.errors.push(`导入配置 ${l.name||"Unknown"} 失败: ${i instanceof Error?i.message:"未知错误"}`)}}catch(r){n.errors.push("解析JSON数据失败: "+(r instanceof Error?r.message:"未知错误"))}return n}generateLLMConfigId(){return`llm_${Date.now()}_${Math.random().toString(36).substring(2,11)}`}createDefaultLLMConfig(t,n,r){return{id:this.generateLLMConfigId(),name:t.name,provider:t.provider.toLowerCase(),model:t.model,apiKey:n,baseURL:r,temperature:t.defaultSettings.temperature,maxTokens:t.defaultSettings.maxTokens}}async getLLMConfig(t){try{return(await this.getLLMConfigs()).find(r=>r.id===t)||null}catch(n){return console.error("Failed to get LLM config:",n),null}}validateLLMConfig(t){var r,l,i,o;const n=[];return(r=t.name)!=null&&r.trim()||n.push("配置名称不能为空"),t.provider||n.push("请选择提供商"),(l=t.model)!=null&&l.trim()||n.push("模型名称不能为空"),(i=t.apiKey)!=null&&i.trim()||n.push("API密钥不能为空"),t.temperature!==void 0&&(t.temperature<0||t.temperature>2)&&n.push("温度值应在0-2之间"),t.maxTokens!==void 0&&(t.maxTokens<1||t.maxTokens>4e3)&&n.push("最大令牌数应在1-4000之间"),t.provider==="azure"&&!((o=t.baseURL)!=null&&o.trim())&&n.push("Azure提供商需要设置基础URL"),n}async getLLMConfigStats(){try{const t=await this.getLLMConfigs(),n={};t.forEach(l=>{n[l.provider]=(n[l.provider]||0)+1});const r=t.slice(0,5);return{total:t.length,byProvider:n,recentlyUsed:r}}catch(t){return console.error("Failed to get LLM config stats:",t),{total:0,byProvider:{},recentlyUsed:[]}}}async saveDiscussions(t){try{const n=await this.getSettings(),r=(n==null?void 0:n.maxStoredDiscussions)||100,l=t.sort((i,o)=>new Date(o.createdAt).getTime()-new Date(i.createdAt).getTime()).slice(0,r);if(this.serverAvailable){console.log(`[StorageService] Starting batch save for ${l.length} discussions`);const i=await this.getDiscussions(),o=l.map(async a=>i.findIndex(f=>f.id===a.id)>=0?V.updateDiscussion(a.id,a):V.createDiscussion(a));await Promise.all(o),console.log(`[StorageService] Successfully saved ${l.length} discussions to server`)}else throw new Error("服务器不可用，无法保存讨论数据");this.updateLastModified()}catch(n){throw console.error("Failed to save discussions:",n),new Error("Failed to save discussions to storage")}}async getDiscussions(){if(this.serverAvailable)return await V.getDiscussions();throw new Error("服务器不可用，无法获取讨论数据")}async saveDiscussion(t){try{if(this.serverAvailable)(await this.getDiscussions()).findIndex(l=>l.id===t.id)>=0?await V.updateDiscussion(t.id,t):await V.createDiscussion(t);else throw new Error("服务器不可用，无法保存讨论")}catch(n){throw console.error("Failed to save discussion:",n),n}}async deleteDiscussion(t){try{if(this.serverAvailable)await V.deleteDiscussion(t);else throw new Error("服务器不可用，无法删除讨论")}catch(n){throw console.error("Failed to delete discussion:",n),n}}async saveSettings(t){if(this.serverAvailable)await V.updateSettings(t);else throw new Error("服务器不可用，无法保存设置")}async getSettings(){if(this.serverAvailable)return await V.getSettings();throw new Error("服务器不可用，无法获取设置数据")}async savePreferences(t){try{if(this.serverAvailable)await V.updatePreferences(t);else throw new Error("服务器不可用，无法保存用户偏好")}catch(n){throw console.error("Failed to save preferences:",n),new Error("Failed to save preferences to storage")}}async getPreferences(){try{if(this.serverAvailable)return await V.getPreferences();throw new Error("服务器不可用，无法获取用户偏好数据")}catch(t){throw console.error("Failed to load preferences:",t),t}}async getAllData(){return{agents:await this.getAgents(),llmConfigs:await this.getLLMConfigs(),discussions:await this.getDiscussions(),settings:await this.getSettings()||{},preferences:await this.getPreferences()||{}}}async importAllData(t){if(this.serverAvailable)await V.importData(t,!1);else throw new Error("服务器不可用，无法导入数据")}async clearAllData(){if(this.serverAvailable)await V.clearAllData();else throw new Error("服务器不可用，无法清除数据")}async updateLastModified(){const t=await this.getSettings();t&&(t.lastUpdated=new Date().toISOString(),await this.saveSettings(t))}validateData(t){try{return!(t.agents&&!Array.isArray(t.agents)||t.llmConfigs&&!Array.isArray(t.llmConfigs)||t.discussions&&!Array.isArray(t.discussions))}catch{return!1}}async getStorageInfo(){if(this.serverAvailable)return await V.getStorageInfo();throw new Error("服务器不可用，无法获取存储信息")}};ge(Vt,"instance");let yi=Vt;const U=yi.getInstance();let Wr;const Vf=new Uint8Array(16);function Hf(){if(!Wr&&(Wr=typeof crypto<"u"&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto),!Wr))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return Wr(Vf)}const ae=[];for(let e=0;e<256;++e)ae.push((e+256).toString(16).slice(1));function Bf(e,t=0){return ae[e[t+0]]+ae[e[t+1]]+ae[e[t+2]]+ae[e[t+3]]+"-"+ae[e[t+4]]+ae[e[t+5]]+"-"+ae[e[t+6]]+ae[e[t+7]]+"-"+ae[e[t+8]]+ae[e[t+9]]+"-"+ae[e[t+10]]+ae[e[t+11]]+ae[e[t+12]]+ae[e[t+13]]+ae[e[t+14]]+ae[e[t+15]]}const Wf=typeof crypto<"u"&&crypto.randomUUID&&crypto.randomUUID.bind(crypto),Aa={randomUUID:Wf};function Ds(e,t,n){if(Aa.randomUUID&&!t&&!e)return Aa.randomUUID();e=e||{};const r=e.random||(e.rng||Hf)();if(r[6]=r[6]&15|64,r[8]=r[8]&63|128,t){n=n||0;for(let l=0;l<16;++l)t[n+l]=r[l];return t}return Bf(r)}const Qf={maxEntriesPerAgent:100,defaultQueryLimit:20,includeOwnMessages:!0,includeOthersMessages:!0,autoCleanup:!0},Ht=class Ht{constructor(t=Qf){ge(this,"memories",new Map);ge(this,"config");this.config=t}static getInstance(t){return Ht.instance||(Ht.instance=new Ht(t)),Ht.instance}getMemoryKey(t,n){return`${t}-${n}`}createMemory(t,n){const r=this.getMemoryKey(t,n);if(this.memories.has(r))return this.memories.get(r);const l={agentId:t,discussionId:n,entries:[],createdAt:new Date,lastUpdated:new Date,maxEntries:this.config.maxEntriesPerAgent};return this.memories.set(r,l),l}getMemory(t,n){const r=this.getMemoryKey(t,n);return this.memories.get(r)||null}addEntry(t,n,r){let l=this.getMemory(t,n);if(l||(l=this.createMemory(t,n)),l.entries.find(a=>a.messageId===r.id))return;const o={id:Ds(),messageId:r.id,content:r.content,type:r.type,timestamp:r.timestamp,discussionId:n,isOwnMessage:r.agentId===t,context:this.generateContext(r,l.entries)};l.entries.push(o),l.lastUpdated=new Date,l.entries.length>l.maxEntries&&l.entries.shift(),this.memories.set(this.getMemoryKey(t,n),l)}generateContext(t,n){const r=n.slice(-3);return r.length===0?"讨论开始":`前文: ${r.map(i=>`${i.isOwnMessage?"我":"他人"}说: ${i.content.substring(0,30)}...`).join(" | ")}`}clearMemory(t,n){const r=this.getMemoryKey(t,n);this.memories.delete(r)}getEntries(t,n,r={}){const l=this.getMemory(t,n);if(!l)return[];let i=[...l.entries];r.includeOwnMessages===!1&&(i=i.filter(a=>!a.isOwnMessage)),r.includeOthersMessages===!1&&(i=i.filter(a=>a.isOwnMessage)),r.messageTypes&&r.messageTypes.length>0&&(i=i.filter(a=>r.messageTypes.includes(a.type))),r.timeRange&&(r.timeRange.start&&(i=i.filter(a=>a.timestamp>=r.timeRange.start)),r.timeRange.end&&(i=i.filter(a=>a.timestamp<=r.timeRange.end)));const o=r.limit||this.config.defaultQueryLimit;return i.slice(-o)}getStats(t,n){const r=this.getMemory(t,n);if(!r)return{totalEntries:0,ownMessages:0,othersMessages:0,messageTypeDistribution:{statement:0,question:0,agreement:0,disagreement:0}};const l={totalEntries:r.entries.length,ownMessages:r.entries.filter(i=>i.isOwnMessage).length,othersMessages:r.entries.filter(i=>!i.isOwnMessage).length,messageTypeDistribution:{statement:0,question:0,agreement:0,disagreement:0}};return r.entries.forEach(i=>{l.messageTypeDistribution[i.type]++}),r.entries.length>0&&(l.oldestEntry=r.entries[0].timestamp,l.newestEntry=r.entries[r.entries.length-1].timestamp),l}buildConversationHistory(t,n,r={}){return this.getEntries(t,n,{limit:r.limit||10,includeOwnMessages:r.includeOwnMessages??!0,includeOthersMessages:r.includeOthersMessages??!0,...r}).map(i=>({role:i.isOwnMessage?"assistant":"user",content:i.isOwnMessage?`我：${i.content}`:`其他参与者：${i.content}`,timestamp:i.timestamp}))}cleanup(t){const n=[];for(const[r,l]of this.memories.entries())l.discussionId===t&&n.push(r);n.forEach(r=>this.memories.delete(r))}getAllMemories(t){const n=new Map;for(const[,r]of this.memories.entries())r.discussionId===t&&n.set(r.agentId,r);return n}updateConfig(t){this.config={...this.config,...t}}getConfig(){return{...this.config}}getServiceInfo(){return{totalMemories:this.memories.size,config:this.config,memoryKeys:Array.from(this.memories.keys())}}};ge(Ht,"instance");let vi=Ht;const Ft=vi.getInstance(),Ju={agents:[],currentDiscussion:null,allDiscussions:[],isDiscussionActive:!1,settings:null,preferences:null,isLoading:!0,loadingStep:"storage"},ed=D.createContext({state:Ju,dispatch:()=>null,addAgent:()=>null,updateAgent:()=>null,deleteAgent:()=>null,startDiscussion:()=>null,setCurrentDiscussion:()=>null,endDiscussion:()=>null,sendMessage:()=>null,updateSettings:()=>null,updatePreferences:()=>null,exportData:async()=>"",importData:async()=>!1,clearAllData:async()=>{}});function Kf(e,t){switch(t.type){case"ADD_AGENT":return{...e,agents:[...e.agents,t.payload]};case"UPDATE_AGENT":return{...e,agents:e.agents.map(o=>o.id===t.payload.id?t.payload:o)};case"DELETE_AGENT":return{...e,agents:e.agents.filter(o=>o.id!==t.payload)};case"START_DISCUSSION":const n={id:Ds(),topic:t.payload.topic,mode:t.payload.mode,participants:t.payload.selectedAgents,messages:[],status:"active",consensus:null,createdAt:new Date,consensusScore:0,moderatorId:t.payload.moderatorId,moderatorSummaries:[],topicRelevanceScore:1,moderatorInterventions:0};return t.payload.selectedAgents.forEach(o=>{Ft.createMemory(o,n.id)}),{...e,currentDiscussion:n,isDiscussionActive:!0};case"SET_CURRENT_DISCUSSION":return t.payload&&t.payload.messages.length>0&&(Ft.cleanup(t.payload.id),t.payload.participants.forEach(o=>{Ft.createMemory(o,t.payload.id)}),t.payload.messages.forEach(o=>{t.payload.participants.forEach(a=>{Ft.addEntry(a,t.payload.id,o)})})),{...e,currentDiscussion:t.payload,isDiscussionActive:!1};case"ADD_MESSAGE":if(!e.currentDiscussion)return e;e.currentDiscussion.participants.forEach(o=>{Ft.addEntry(o,e.currentDiscussion.id,t.payload)});const r={...e.currentDiscussion,messages:[...e.currentDiscussion.messages,t.payload]};return{...e,currentDiscussion:r};case"UPDATE_CONSENSUS":if(!e.currentDiscussion)return e;const l={...e.currentDiscussion,consensusScore:t.payload.consensusScore,consensus:t.payload.consensus||e.currentDiscussion.consensus,status:t.payload.consensusScore>80?"consensus":e.currentDiscussion.status,moderatorInterventions:t.payload.moderatorInterventions??e.currentDiscussion.moderatorInterventions,topicRelevanceScore:t.payload.topicRelevanceScore??e.currentDiscussion.topicRelevanceScore,moderatorSummaries:t.payload.moderatorSummaries??e.currentDiscussion.moderatorSummaries};return{...e,currentDiscussion:l};case"END_DISCUSSION":if(!e.currentDiscussion)return e;Ft.cleanup(e.currentDiscussion.id);const i={...e.currentDiscussion,status:"ended"};return{...e,currentDiscussion:null,allDiscussions:[i,...e.allDiscussions],isDiscussionActive:!1};case"LOAD_STATE":return t.payload;case"UPDATE_SETTINGS":return{...e,settings:t.payload};case"UPDATE_PREFERENCES":return{...e,preferences:t.payload};case"SET_LOADING":return{...e,isLoading:t.payload};case"SET_LOADING_STEP":return{...e,loadingStep:t.payload};case"SET_ALL_DISCUSSIONS":return{...e,allDiscussions:t.payload};case"INITIALIZE_SUCCESS":return{...e,agents:t.payload.agents,allDiscussions:t.payload.discussions,settings:t.payload.settings,preferences:t.payload.preferences,isLoading:!1,loadingStep:"complete"};default:return e}}function Gf({children:e}){const[t,n]=D.useReducer(Kf,Ju);D.useEffect(()=>{let S=!1;return(async()=>{if(S)return;const d=Date.now();try{console.log("Starting app initialization..."),n({type:"SET_LOADING",payload:!0}),n({type:"SET_LOADING_STEP",payload:"storage"}),console.log("Initializing storage service...");const u=Date.now();if(await U.initialize(),S||(console.log(`Storage service initialized in ${Date.now()-u}ms`),n({type:"SET_LOADING_STEP",payload:"server"}),U.isServerAvailable()||console.warn("后端服务器不可用，系统无法正常工作"),S))return;n({type:"SET_LOADING_STEP",payload:"agents"}),console.log("Loading data...");const j=Date.now(),w=await y();n({type:"SET_LOADING_STEP",payload:"discussions"}),console.log(`Data loaded in ${Date.now()-j}ms`),n({type:"INITIALIZE_SUCCESS",payload:w}),console.log(`App initialization completed in ${Date.now()-d}ms`)}catch(u){throw console.error("Failed to initialize app:",u),console.error("Error details:",{message:u instanceof Error?u.message:"Unknown error",stack:u instanceof Error?u.stack:void 0,initTime:Date.now()-d}),u}})(),()=>{S=!0}},[]),D.useEffect(()=>{if(!t.isLoading){const S=setTimeout(async()=>{try{console.log("Auto-saving data...");const m=[];m.push(U.saveAgents(t.agents));const d=t.currentDiscussion?[...t.allDiscussions,t.currentDiscussion]:t.allDiscussions;m.push(U.saveDiscussions(d)),t.settings&&m.push(U.saveSettings(t.settings)),t.preferences&&m.push(U.savePreferences(t.preferences)),await Promise.all(m),console.log("Auto-save completed")}catch(m){console.error("Failed to auto-save data:",m)}},1e3);return()=>clearTimeout(S)}},[t.agents,t.allDiscussions,t.currentDiscussion,t.settings,t.preferences,t.isLoading]);const r=S=>{const m={...S,id:Ds(),isActive:!0};n({type:"ADD_AGENT",payload:m})},l=S=>{n({type:"UPDATE_AGENT",payload:S})},i=async S=>{try{await U.deleteAgent(S),n({type:"DELETE_AGENT",payload:S})}catch(m){throw console.error("Failed to delete agent:",m),m}},o=S=>{n({type:"START_DISCUSSION",payload:S})},a=S=>{n({type:"SET_CURRENT_DISCUSSION",payload:S})},c=async S=>{if(t.currentDiscussion){n({type:"END_DISCUSSION"});try{const m={...t.currentDiscussion,status:"ended",endReason:S||"手动终止"};await U.saveDiscussion(m);const d=await U.getDiscussions();n({type:"SET_ALL_DISCUSSIONS",payload:d})}catch(m){console.error("Failed to save discussion:",m)}}else n({type:"END_DISCUSSION"})},f=async(S,m,d)=>{var p;if(!t.currentDiscussion||t.currentDiscussion.status!=="active"||!t.isDiscussionActive){console.log("拒绝发送消息 - 讨论已结束或不活跃:",{hasDiscussion:!!t.currentDiscussion,status:(p=t.currentDiscussion)==null?void 0:p.status,isActive:t.isDiscussionActive,agentId:m,content:S.substring(0,50)+"..."});return}const u={id:Ds(),agentId:m,content:S,type:d,timestamp:new Date};if(n({type:"ADD_MESSAGE",payload:u}),t.currentDiscussion)try{await V.addMessage(t.currentDiscussion.id,u)}catch(j){console.error("Failed to save message:",j)}},x=S=>{n({type:"UPDATE_SETTINGS",payload:S})},g=S=>{n({type:"UPDATE_PREFERENCES",payload:S})},y=async()=>{try{const[S,m,d,u]=await Promise.all([U.getAgents(),U.getDiscussions(),U.getSettings(),U.getPreferences()]);return{agents:S||[],discussions:m||[],settings:d||{},preferences:u||{}}}catch(S){throw console.error("Failed to load data:",S),S}},v=async()=>{try{const S=await U.getAllData();return JSON.stringify(S,null,2)}catch(S){throw console.error("Failed to export data:",S),new Error("导出数据失败")}},N=async S=>{try{const m=JSON.parse(S);if(!U.validateData(m))throw new Error("数据格式无效");await U.importAllData(m);const d=await y();return n({type:"INITIALIZE_SUCCESS",payload:d}),!0}catch(m){return console.error("Failed to import data:",m),!1}},h=async()=>{try{await U.clearAllData(),n({type:"INITIALIZE_SUCCESS",payload:{agents:[],discussions:[],settings:{},preferences:{}}})}catch(S){throw console.error("Failed to clear data:",S),new Error("清除数据失败")}};return s.jsx(ed.Provider,{value:{state:t,dispatch:n,addAgent:r,updateAgent:l,deleteAgent:i,startDiscussion:o,setCurrentDiscussion:a,endDiscussion:c,sendMessage:f,updateSettings:x,updatePreferences:g,exportData:v,importData:N,clearAllData:h},children:e})}const Pt=()=>{const e=D.useContext(ed);if(!e)throw new Error("useApp must be used within AppProvider");return e};var qf={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};const Yf=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),Xf=(e,t)=>{const n=D.forwardRef(({color:r="currentColor",size:l=24,strokeWidth:i=2,absoluteStrokeWidth:o,children:a,...c},f)=>D.createElement("svg",{ref:f,...qf,width:l,height:l,stroke:r,strokeWidth:o?Number(i)*24/Number(l):i,className:`lucide lucide-${Yf(e)}`,...c},[...t.map(([x,g])=>D.createElement(x,g)),...(Array.isArray(a)?a:[a])||[]]));return n.displayName=`${e}`,n};var P=Xf;const An=P("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]),Ms=P("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),kl=P("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]),Et=P("Brain",[["path",{d:"M9.5 2A2.5 2.5 0 0 1 12 4.5v15a2.5 2.5 0 0 1-4.96.44 2.5 2.5 0 0 1-2.96-3.08 3 3 0 0 1-.34-5.58 2.5 2.5 0 0 1 1.32-4.24 2.5 2.5 0 0 1 1.98-3A2.5 2.5 0 0 1 9.5 2Z",key:"1mhkh5"}],["path",{d:"M14.5 2A2.5 2.5 0 0 0 12 4.5v15a2.5 2.5 0 0 0 4.96.44 2.5 2.5 0 0 0 2.96-3.08 3 3 0 0 0 .34-5.58 2.5 2.5 0 0 0-1.32-4.24 2.5 2.5 0 0 0-1.98-3A2.5 2.5 0 0 0 14.5 2Z",key:"1d6s00"}]]),Zf=P("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]]),tt=P("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["polyline",{points:"22 4 12 14.01 9 11.01",key:"6xbx8j"}]]),Jf=P("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]),ep=P("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]),Ts=P("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]),td=P("Cpu",[["rect",{x:"4",y:"4",width:"16",height:"16",rx:"2",key:"1vbyd7"}],["rect",{x:"9",y:"9",width:"6",height:"6",key:"o3kz5p"}],["path",{d:"M15 2v2",key:"13l42r"}],["path",{d:"M15 20v2",key:"15mkzm"}],["path",{d:"M2 15h2",key:"1gxd5l"}],["path",{d:"M2 9h2",key:"1bbxkp"}],["path",{d:"M20 15h2",key:"19e6y8"}],["path",{d:"M20 9h2",key:"19tzq7"}],["path",{d:"M9 2v2",key:"165o2o"}],["path",{d:"M9 20v2",key:"i2bqo8"}]]),vo=P("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]),ji=P("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]]),tp=P("ExternalLink",[["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}],["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["line",{x1:"10",x2:"21",y1:"14",y2:"3",key:"18c3s4"}]]),np=P("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]]),rp=P("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),sp=P("FileText",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["line",{x1:"16",x2:"8",y1:"13",y2:"13",key:"14keom"}],["line",{x1:"16",x2:"8",y1:"17",y2:"17",key:"17nazh"}],["line",{x1:"10",x2:"8",y1:"9",y2:"9",key:"1a5vjj"}]]),lp=P("HardDrive",[["line",{x1:"22",x2:"2",y1:"12",y2:"12",key:"1y58io"}],["path",{d:"M5.45 5.11 2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z",key:"oot6mr"}],["line",{x1:"6",x2:"6.01",y1:"16",y2:"16",key:"sgf278"}],["line",{x1:"10",x2:"10.01",y1:"16",y2:"16",key:"1l4acy"}]]),nd=P("HelpCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),As=P("History",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]]),ip=P("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]),Ia=P("Loader",[["line",{x1:"12",x2:"12",y1:"2",y2:"6",key:"gza1u7"}],["line",{x1:"12",x2:"12",y1:"18",y2:"22",key:"1qhbu9"}],["line",{x1:"4.93",x2:"7.76",y1:"4.93",y2:"7.76",key:"xae44r"}],["line",{x1:"16.24",x2:"19.07",y1:"16.24",y2:"19.07",key:"bxnmvf"}],["line",{x1:"2",x2:"6",y1:"12",y2:"12",key:"89khin"}],["line",{x1:"18",x2:"22",y1:"12",y2:"12",key:"pb8tfm"}],["line",{x1:"4.93",x2:"7.76",y1:"19.07",y2:"16.24",key:"1uxjnu"}],["line",{x1:"16.24",x2:"19.07",y1:"7.76",y2:"4.93",key:"6duxfx"}]]),Pa=P("MessageCircle",[["path",{d:"m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z",key:"v2veuj"}]]),Be=P("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]]),rd=P("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]]),op=P("Play",[["polygon",{points:"5 3 19 12 5 21 5 3",key:"191637"}]]),wi=P("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]),ap=P("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]),cp=P("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]]),up=P("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]),Lt=P("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),_a=P("StopCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["rect",{width:"6",height:"6",x:"9",y:"9",key:"1wrtvo"}]]),dp=P("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]),mp=P("TestTube",[["path",{d:"M14.5 2v17.5c0 1.4-1.1 2.5-2.5 2.5h0c-1.4 0-2.5-1.1-2.5-2.5V2",key:"187lwq"}],["path",{d:"M8.5 2h7",key:"csnxdl"}],["path",{d:"M14.5 16h-5",key:"1ox875"}]]),sd=P("ThumbsDown",[["path",{d:"M17 14V2",key:"8ymqnk"}],["path",{d:"M9 18.12 10 14H4.17a2 2 0 0 1-1.92-2.56l2.33-8A2 2 0 0 1 6.5 2H20a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2h-2.76a2 2 0 0 0-1.79 1.11L12 22h0a3.13 3.13 0 0 1-3-3.88Z",key:"s6e0r"}]]),ld=P("ThumbsUp",[["path",{d:"M7 10v12",key:"1qc93n"}],["path",{d:"M15 5.88 14 10h5.83a2 2 0 0 1 1.92 2.56l-2.33 8A2 2 0 0 1 17.5 22H4a2 2 0 0 1-2-2v-8a2 2 0 0 1 2-2h2.76a2 2 0 0 0 1.79-1.11L12 2h0a3.13 3.13 0 0 1 3 3.88Z",key:"y3tblf"}]]),jr=P("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]),id=P("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]]),Ni=P("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]]),fp=P("UserX",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"17",x2:"22",y1:"8",y2:"13",key:"3nzzx3"}],["line",{x1:"22",x2:"17",y1:"8",y2:"13",key:"1swrse"}]]),pp=P("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]),st=P("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]),hp=P("Wrench",[["path",{d:"M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z",key:"cbrjhi"}]]),gp=P("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),za=P("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]]),xp=[{id:"gpt-4-turbo",name:"GPT-4 Turbo",provider:"OpenAI",model:"gpt-4-turbo-preview",description:"OpenAI最新的GPT-4 Turbo模型，性能强大，适合复杂推理",defaultSettings:{temperature:.7,maxTokens:1e3}},{id:"gpt-4",name:"GPT-4",provider:"OpenAI",model:"gpt-4",description:"OpenAI的旗舰模型，适合需要高质量输出的场景",defaultSettings:{temperature:.7,maxTokens:1e3}},{id:"gpt-3.5-turbo",name:"GPT-3.5 Turbo",provider:"OpenAI",model:"gpt-3.5-turbo",description:"快速且经济的模型，适合大多数对话任务",defaultSettings:{temperature:.7,maxTokens:800}},{id:"claude-3-opus",name:"Claude 3 Opus",provider:"Anthropic",model:"claude-3-opus-20240229",description:"Anthropic最强大的模型，擅长复杂分析和创作",defaultSettings:{temperature:.7,maxTokens:1e3}},{id:"claude-3-sonnet",name:"Claude 3 Sonnet",provider:"Anthropic",model:"claude-3-sonnet-20240229",description:"平衡性能和成本的优秀选择",defaultSettings:{temperature:.7,maxTokens:800}},{id:"claude-3-haiku",name:"Claude 3 Haiku",provider:"Anthropic",model:"claude-3-haiku-20240307",description:"快速响应的轻量级模型，适合简单任务",defaultSettings:{temperature:.7,maxTokens:600}},{id:"gpt-4-azure",name:"Azure GPT-4",provider:"Azure",model:"gpt-4",description:"部署在Azure上的GPT-4模型",defaultSettings:{temperature:.7,maxTokens:1e3}},{id:"gpt-35-turbo-azure",name:"Azure GPT-3.5 Turbo",provider:"Azure",model:"gpt-35-turbo",description:"部署在Azure上的GPT-3.5 Turbo模型",defaultSettings:{temperature:.7,maxTokens:800}}],Ra=xp;function Is(e){return{openai:"🤖",anthropic:"🧠",azure:"☁️",custom:"⚙️"}[e.toLowerCase()]||"🔧"}function Ps(e){return{openai:"bg-green-100 text-green-800",anthropic:"bg-blue-100 text-blue-800",azure:"bg-purple-100 text-purple-800",custom:"bg-gray-100 text-gray-800"}[e.toLowerCase()]||"bg-gray-100 text-gray-800"}const $a=["/images/agent-alex.jpg","/images/agent-luna.jpg","/images/agent-max.jpg","/images/agent-chen.jpg","/images/agent-sam.jpg","/images/agent-robin.jpg","/images/agent-taylor.jpg","/images/agent-zoe.jpg"],yp=["技术","商业","设计","营销","数据分析","产品管理","法律","心理学","教育","医疗"],od=[{value:"logical",label:"逻辑型"},{value:"creative",label:"创意型"},{value:"analytical",label:"分析型"},{value:"intuitive",label:"直觉型"},{value:"systematic",label:"系统型"}],vp=[{value:"assertive",label:"果断型"},{value:"collaborative",label:"协作型"},{value:"diplomatic",label:"外交型"},{value:"direct",label:"直接型"},{value:"thoughtful",label:"深思型"}],jp=["数据查询","市场分析","技术调研","用户调研","竞品分析","风险评估","法律咨询","创意生成"];function wp(){const{state:e,addAgent:t,updateAgent:n,deleteAgent:r}=Pt(),[l,i]=D.useState(!1),[o,a]=D.useState(null),c=async x=>{if(confirm("确定要删除这个智能体吗？"))try{await r(x)}catch(g){alert("删除智能体失败: "+(g instanceof Error?g.message:"未知错误"))}},f=x=>{o?(n({...x,id:o.id,isActive:o.isActive}),a(null)):(t(x),i(!1))};return s.jsx("div",{className:"h-full bg-gradient-to-br from-slate-50 to-blue-50 w-full overflow-y-auto",children:s.jsx("div",{className:"centered-container",children:s.jsxs("div",{className:"centered-content",children:[s.jsxs("div",{className:"flex items-center justify-between mb-8",children:[s.jsxs("div",{children:[s.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"智能体管理"}),s.jsx("p",{className:"text-gray-600",children:"配置和管理您的AI智能体团队"})]}),s.jsxs("button",{onClick:()=>i(!0),className:"flex items-center gap-2 bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors shadow-lg",children:[s.jsx(wi,{size:20}),"添加智能体"]})]}),s.jsx("div",{className:"flex flex-wrap gap-6 mb-8",children:e.agents.map(x=>s.jsx(Np,{agent:x,onEdit:()=>a(x),onDelete:()=>c(x.id)},x.id))}),(l||o)&&s.jsx(Sp,{agent:o,onSubmit:f,onCancel:()=>{i(!1),a(null)}})]})})})}function Np({agent:e,onEdit:t,onDelete:n}){var r,l,i;return s.jsxs("div",{className:"bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow p-6 border border-gray-100 fixed-size-card",children:[s.jsxs("div",{className:"flex items-center gap-4 mb-4",children:[s.jsx("img",{src:e.avatar,alt:e.name,className:"w-16 h-16 rounded-full object-cover border-4 border-blue-100"}),s.jsxs("div",{children:[s.jsx("h3",{className:"text-xl font-semibold text-gray-900",children:e.name}),s.jsx("span",{className:`inline-block px-2 py-1 rounded-full text-xs font-medium ${e.isActive?"bg-green-100 text-green-800":"bg-gray-100 text-gray-600"}`,children:e.isActive?"活跃":"非活跃"})]})]}),s.jsxs("div",{className:"space-y-3 mb-4",children:[s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(pp,{size:16,className:"text-blue-600"}),s.jsx("span",{className:"text-sm text-gray-600",children:"专业领域："}),s.jsxs("div",{className:"flex flex-wrap gap-1",children:[e.expertise.slice(0,2).map((o,a)=>s.jsx("span",{className:"bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded",children:o},a)),e.expertise.length>2&&s.jsxs("span",{className:"text-xs text-gray-500",children:["+",e.expertise.length-2]})]})]}),s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(Et,{size:16,className:"text-purple-600"}),s.jsx("span",{className:"text-sm text-gray-600",children:"思维方式："}),s.jsx("span",{className:"text-sm font-medium text-gray-900",children:(r=od.find(o=>o.value===e.thinkingStyle))==null?void 0:r.label})]}),s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(hp,{size:16,className:"text-green-600"}),s.jsx("span",{className:"text-sm text-gray-600",children:"工具："}),s.jsxs("span",{className:"text-sm text-gray-500",children:[e.tools.length," 个"]})]}),e.llmConfig&&s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(Lt,{size:16,className:"text-orange-600"}),s.jsx("span",{className:"text-sm text-gray-600",children:"LLM："}),s.jsxs("div",{className:"flex items-center gap-1",children:[s.jsx("span",{className:"text-sm",children:Is(e.llmConfig.provider)}),s.jsx("span",{className:`text-xs px-2 py-0.5 rounded ${Ps(e.llmConfig.provider)}`,children:e.llmConfig.name})]})]}),e.isModerator&&s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx("div",{className:"w-4 h-4 bg-purple-600 rounded-full flex items-center justify-center",children:s.jsx("span",{className:"text-white text-xs font-bold",children:"主"})}),s.jsx("span",{className:"text-sm text-gray-600",children:"可做主持人"}),s.jsx("span",{className:"text-xs bg-purple-100 text-purple-800 px-2 py-0.5 rounded",children:((l=e.moderatorConfig)==null?void 0:l.managementStyle)==="strict"?"严格型":((i=e.moderatorConfig)==null?void 0:i.managementStyle)==="flexible"?"灵活型":"协作型"})]})]}),s.jsxs("div",{className:"flex gap-2",children:[s.jsxs("button",{onClick:t,className:"flex-1 bg-blue-50 text-blue-600 py-2 rounded-lg hover:bg-blue-100 transition-colors flex items-center justify-center gap-1",children:[s.jsx(rd,{size:16}),"编辑"]}),s.jsxs("button",{onClick:n,className:"flex-1 bg-red-50 text-red-600 py-2 rounded-lg hover:bg-red-100 transition-colors flex items-center justify-center gap-1",children:[s.jsx(jr,{size:16}),"删除"]})]})]})}function Sp({agent:e,onSubmit:t,onCancel:n}){var y;const[r,l]=D.useState({name:(e==null?void 0:e.name)||"",avatar:(e==null?void 0:e.avatar)||$a[0],expertise:(e==null?void 0:e.expertise)||[],thinkingStyle:(e==null?void 0:e.thinkingStyle)||"logical",personality:(e==null?void 0:e.personality)||"collaborative",tools:(e==null?void 0:e.tools)||[],llmConfig:(e==null?void 0:e.llmConfig)||null,isModerator:(e==null?void 0:e.isModerator)||!1,moderatorConfig:(e==null?void 0:e.moderatorConfig)||{summaryFrequency:5,interventionThreshold:.6,managementStyle:"flexible",autoTerminate:!0,maxInterventions:5}}),[i,o]=D.useState([]),[a,c]=D.useState(!0);D.useEffect(()=>{(async()=>{try{c(!0);const N=await U.getLLMConfigs();o(N)}catch(N){console.error("Failed to load LLM configs:",N),o([])}finally{c(!1)}})()},[]);const f=v=>{v.preventDefault(),r.name&&r.expertise.length>0&&r.llmConfig&&t({...r,llmConfig:r.llmConfig,isModerator:r.isModerator,moderatorConfig:r.isModerator?r.moderatorConfig:void 0})},x=v=>{l(N=>({...N,expertise:N.expertise.includes(v)?N.expertise.filter(h=>h!==v):[...N.expertise,v]}))},g=v=>{l(N=>({...N,tools:N.tools.includes(v)?N.tools.filter(h=>h!==v):[...N.tools,v]}))};return s.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:s.jsxs("div",{className:"bg-white rounded-xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:[s.jsx("div",{className:"p-6 border-b border-gray-200",children:s.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:e?"编辑智能体":"添加新智能体"})}),s.jsxs("form",{onSubmit:f,className:"p-6 space-y-6",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"智能体名称"}),s.jsx("input",{type:"text",value:r.name,onChange:v=>l(N=>({...N,name:v.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"输入智能体名称",required:!0})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"选择头像"}),s.jsx("div",{className:"grid grid-cols-8 gap-3",children:$a.map((v,N)=>s.jsx("button",{type:"button",onClick:()=>l(h=>({...h,avatar:v})),className:`relative rounded-lg overflow-hidden border-4 transition-all ${r.avatar===v?"border-blue-500 ring-2 ring-blue-200":"border-gray-200 hover:border-gray-300"}`,children:s.jsx("img",{src:v,alt:`Avatar ${N+1}`,className:"w-16 h-16 object-cover"})},N))})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"专业领域（至少选择一个）"}),s.jsx("div",{className:"grid grid-cols-2 gap-2",children:yp.map(v=>s.jsx("button",{type:"button",onClick:()=>x(v),className:`px-3 py-2 rounded-lg text-sm transition-colors ${r.expertise.includes(v)?"bg-blue-100 text-blue-800 border-2 border-blue-300":"bg-gray-100 text-gray-700 border-2 border-gray-300 hover:bg-gray-200"}`,children:v},v))})]}),s.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"思维方式"}),s.jsx("select",{value:r.thinkingStyle,onChange:v=>l(N=>({...N,thinkingStyle:v.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:od.map(v=>s.jsx("option",{value:v.value,children:v.label},v.value))})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"性格特征"}),s.jsx("select",{value:r.personality,onChange:v=>l(N=>({...N,personality:v.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:vp.map(v=>s.jsx("option",{value:v.value,children:v.label},v.value))})]})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"可用工具"}),s.jsx("div",{className:"grid grid-cols-2 gap-2",children:jp.map(v=>s.jsx("button",{type:"button",onClick:()=>g(v),className:`px-3 py-2 rounded-lg text-sm transition-colors ${r.tools.includes(v)?"bg-green-100 text-green-800 border-2 border-green-300":"bg-gray-100 text-gray-700 border-2 border-gray-300 hover:bg-gray-200"}`,children:v},v))})]}),s.jsxs("div",{children:[s.jsxs("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["LLM配置 ",s.jsx("span",{className:"text-red-500",children:"*"})]}),a?s.jsxs("div",{className:"w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 flex items-center gap-2",children:[s.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"}),s.jsx("span",{className:"text-gray-600",children:"正在加载LLM配置..."})]}):s.jsxs("select",{value:((y=r.llmConfig)==null?void 0:y.id)||"",onChange:v=>{const N=i.find(h=>h.id===v.target.value);l(h=>({...h,llmConfig:N||null}))},className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",required:!0,children:[s.jsx("option",{value:"",children:"请选择LLM配置"}),i.map(v=>s.jsxs("option",{value:v.id,children:[Is(v.provider)," ",v.name," (",v.provider.toUpperCase(),")"]},v.id))]}),r.llmConfig&&s.jsxs("div",{className:"mt-2 p-3 bg-gray-50 rounded-lg",children:[s.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[s.jsx("span",{className:"text-sm font-medium text-gray-700",children:"已选择:"}),s.jsx("span",{className:`text-xs px-2 py-1 rounded ${Ps(r.llmConfig.provider)}`,children:r.llmConfig.name})]}),s.jsxs("div",{className:"text-xs text-gray-600",children:["模型: ",r.llmConfig.model," | 温度: ",r.llmConfig.temperature," | 令牌: ",r.llmConfig.maxTokens]})]}),i.length===0&&s.jsx("div",{className:"mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg",children:s.jsxs("p",{className:"text-sm text-yellow-800",children:["暂无可用的LLM配置。请先前往",s.jsx("span",{className:"font-medium",children:"LLM配置"}),"页面创建LLM配置，然后再创建智能体。"]})})]}),s.jsxs("div",{className:"border-t border-gray-200 pt-6",children:[s.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[s.jsx("input",{type:"checkbox",id:"isModerator",checked:r.isModerator,onChange:v=>l(N=>({...N,isModerator:v.target.checked})),className:"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"}),s.jsx("label",{htmlFor:"isModerator",className:"text-sm font-medium text-gray-700",children:"可以做主持人"})]}),r.isModerator&&s.jsxs("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 space-y-4",children:[s.jsx("h4",{className:"font-medium text-blue-900 mb-3",children:"主持人配置"}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"总结频率（每N条消息）"}),s.jsx("input",{type:"number",min:"1",max:"20",value:r.moderatorConfig.summaryFrequency,onChange:v=>l(N=>({...N,moderatorConfig:{...N.moderatorConfig,summaryFrequency:parseInt(v.target.value)||5}})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"干预阈值（0-1）"}),s.jsx("input",{type:"number",min:"0",max:"1",step:"0.1",value:r.moderatorConfig.interventionThreshold,onChange:v=>l(N=>({...N,moderatorConfig:{...N.moderatorConfig,interventionThreshold:parseFloat(v.target.value)||.6}})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"管理风格"}),s.jsxs("select",{value:r.moderatorConfig.managementStyle,onChange:v=>l(N=>({...N,moderatorConfig:{...N.moderatorConfig,managementStyle:v.target.value}})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[s.jsx("option",{value:"strict",children:"严格型"}),s.jsx("option",{value:"flexible",children:"灵活型"}),s.jsx("option",{value:"collaborative",children:"协作型"})]})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"最大干预次数"}),s.jsx("input",{type:"number",min:"1",max:"20",value:r.moderatorConfig.maxInterventions,onChange:v=>l(N=>({...N,moderatorConfig:{...N.moderatorConfig,maxInterventions:parseInt(v.target.value)||5}})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})]}),s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx("input",{type:"checkbox",id:"autoTerminate",checked:r.moderatorConfig.autoTerminate,onChange:v=>l(N=>({...N,moderatorConfig:{...N.moderatorConfig,autoTerminate:v.target.checked}})),className:"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"}),s.jsx("label",{htmlFor:"autoTerminate",className:"text-sm font-medium text-gray-700",children:"自动终止讨论"})]})]})]}),s.jsxs("div",{className:"flex gap-3 pt-4 border-t border-gray-200",children:[s.jsx("button",{type:"button",onClick:n,className:"flex-1 px-4 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors",children:"取消"}),s.jsx("button",{type:"submit",disabled:a||i.length===0,className:"flex-1 px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors",children:a?"加载中...":`${e?"更新":"创建"}智能体`})]})]})]})})}function kp(){var x,g,y,v,N;const{state:e,startDiscussion:t}=Pt(),[n,r]=D.useState({topic:"",mode:"free",selectedAgents:[],moderatorId:void 0}),[l,i]=D.useState([]),o=()=>{const h=[];return n.topic.trim()||h.push("请输入讨论话题"),n.selectedAgents.length<2&&h.push("至少需要选择2个智能体参与讨论"),n.selectedAgents.length>8&&h.push("最多支持8个智能体同时讨论"),n.mode==="moderator"&&!n.moderatorId&&(n.selectedAgents.map(m=>f.find(d=>d.id===m)).filter(m=>m&&m.isModerator).length===0?h.push("主持人模式需要至少一个具备主持人能力的智能体"):h.push("主持人模式下请选择一个主持人")),i(h),h.length===0},a=()=>{o()&&t(n)},c=h=>{r(S=>({...S,selectedAgents:S.selectedAgents.includes(h)?S.selectedAgents.filter(m=>m!==h):[...S.selectedAgents,h]}))},f=e.agents.filter(h=>h.isActive);return f.length===0?s.jsx("div",{className:"h-full bg-gradient-to-br from-orange-50 to-red-50 overflow-y-auto",children:s.jsx("div",{className:"max-w-4xl mx-auto p-6",children:s.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-8 text-center",children:[s.jsx(An,{size:64,className:"text-orange-500 mx-auto mb-4"}),s.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"没有可用的智能体"}),s.jsx("p",{className:"text-gray-600 mb-6",children:"您需要先创建和配置智能体才能开始讨论。"}),s.jsx("button",{onClick:()=>window.location.reload(),className:"bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors",children:"前往智能体管理"})]})})}):s.jsx("div",{className:"h-full bg-gradient-to-br from-purple-50 to-pink-50 w-full overflow-y-auto",children:s.jsx("div",{className:"flex justify-center p-6",children:s.jsxs("div",{className:"bg-white rounded-xl shadow-lg overflow-hidden",style:{width:"800px",maxWidth:"800px"},children:[s.jsxs("div",{className:"bg-gradient-to-r from-purple-600 to-pink-600 text-white p-8",children:[s.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[s.jsx(Pa,{size:32}),s.jsx("h1",{className:"text-3xl font-bold",children:"创建新讨论"})]}),s.jsx("p",{className:"text-purple-100",children:"配置讨论话题、模式和参与者，开始智能体之间的协作讨论"})]}),s.jsxs("div",{className:"p-8 space-y-8",children:[l.length>0&&s.jsxs("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:[s.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[s.jsx(An,{size:20,className:"text-red-600"}),s.jsx("h3",{className:"font-medium text-red-800",children:"配置错误"})]}),s.jsx("ul",{className:"text-red-700 text-sm space-y-1",children:l.map((h,S)=>s.jsxs("li",{children:["• ",h]},S))})]}),s.jsxs("div",{className:"space-y-3",children:[s.jsx("label",{className:"block text-lg font-semibold text-gray-900",children:"讨论话题"}),s.jsx("textarea",{value:n.topic,onChange:h=>r(S=>({...S,topic:h.target.value})),placeholder:"请输入您想要讨论的话题，例如：如何提升用户体验设计质量？",className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent resize-none",rows:3}),s.jsx("p",{className:"text-sm text-gray-500",children:"清晰的话题描述有助于智能体更好地理解和参与讨论"})]}),s.jsxs("div",{className:"space-y-4",children:[s.jsx("label",{className:"block text-lg font-semibold text-gray-900",children:"讨论模式"}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsxs("button",{onClick:()=>r(h=>({...h,mode:"free"})),className:`p-6 rounded-xl border-2 transition-all text-left ${n.mode==="free"?"border-purple-500 bg-purple-50":"border-gray-200 hover:border-gray-300"}`,children:[s.jsxs("div",{className:"flex items-center gap-3 mb-3",children:[s.jsx(Pa,{size:24,className:n.mode==="free"?"text-purple-600":"text-gray-600"}),s.jsx("h3",{className:"font-semibold text-gray-900",children:"自由讨论模式"})]}),s.jsx("p",{className:"text-gray-600 text-sm",children:"智能体根据话题相关性和兴趣自主发言，讨论更加自然流畅"})]}),s.jsxs("button",{onClick:()=>r(h=>({...h,mode:"moderator"})),className:`p-6 rounded-xl border-2 transition-all text-left ${n.mode==="moderator"?"border-purple-500 bg-purple-50":"border-gray-200 hover:border-gray-300"}`,children:[s.jsxs("div",{className:"flex items-center gap-3 mb-3",children:[s.jsx(Lt,{size:24,className:n.mode==="moderator"?"text-purple-600":"text-gray-600"}),s.jsx("h3",{className:"font-semibold text-gray-900",children:"主持人模式"})]}),s.jsx("p",{className:"text-gray-600 text-sm",children:"选择一个智能体作为主持人，按轮次组织讨论，更加有序规范"})]})]})]}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx(st,{size:24,className:"text-purple-600"}),s.jsxs("h2",{className:"text-lg font-semibold text-gray-900",children:["选择参与者 (",n.selectedAgents.length,"/8)"]})]}),s.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:f.map(h=>s.jsxs("button",{onClick:()=>c(h.id),className:`p-4 rounded-xl border-2 transition-all text-left ${n.selectedAgents.includes(h.id)?"border-purple-500 bg-purple-50":"border-gray-200 hover:border-gray-300"}`,children:[s.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[s.jsx("img",{src:h.avatar,alt:h.name,className:"w-10 h-10 rounded-full object-cover"}),s.jsxs("div",{children:[s.jsx("h3",{className:"font-medium text-gray-900",children:h.name}),s.jsx("p",{className:"text-sm text-gray-500",children:h.expertise.slice(0,2).join("、")})]})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsxs("span",{className:"text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded",children:[h.thinkingStyle==="logical"&&"逻辑型",h.thinkingStyle==="creative"&&"创意型",h.thinkingStyle==="analytical"&&"分析型",h.thinkingStyle==="intuitive"&&"直觉型",h.thinkingStyle==="systematic"&&"系统型"]}),h.isModerator&&s.jsx("span",{className:"text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded",children:"主持人"})]}),n.selectedAgents.includes(h.id)&&s.jsx("div",{className:"w-5 h-5 bg-purple-500 rounded-full flex items-center justify-center",children:s.jsx("div",{className:"w-2 h-2 bg-white rounded-full"})})]})]},h.id))}),s.jsx("p",{className:"text-sm text-gray-500",children:"建议选择具有不同专业背景和思维方式的智能体，以获得更丰富的讨论视角"})]}),n.selectedAgents.length>0&&s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx(Lt,{size:24,className:"text-purple-600"}),s.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"主持人设置"})]}),s.jsxs("div",{className:"space-y-3",children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"选择主持人（可选）"}),s.jsxs("select",{value:n.moderatorId||"",onChange:h=>r(S=>({...S,moderatorId:h.target.value||void 0})),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent",children:[s.jsx("option",{value:"",children:"无主持人（系统自动管理）"}),n.selectedAgents.map(h=>f.find(S=>S.id===h)).filter(h=>h&&h.isModerator).map(h=>s.jsxs("option",{value:h.id,children:[h.name," - ",h.expertise.slice(0,2).join("、")]},h.id))]}),n.moderatorId&&s.jsxs("div",{className:"space-y-4",children:[(()=>{var S,m,d;const h=f.find(u=>u.id===n.moderatorId);return h?s.jsx("div",{className:"bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-4",children:s.jsxs("div",{className:"flex items-start gap-4",children:[s.jsx("img",{src:h.avatar,alt:h.name,className:"w-12 h-12 rounded-full object-cover"}),s.jsxs("div",{className:"flex-1",children:[s.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[s.jsx("h4",{className:"font-semibold text-gray-900",children:h.name}),s.jsx("span",{className:"text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded",children:"主持人"})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3 text-sm",children:[s.jsxs("div",{children:[s.jsx("span",{className:"text-gray-600",children:"专业领域："}),s.jsx("span",{className:"text-gray-900",children:h.expertise.slice(0,3).join("、")})]}),s.jsxs("div",{children:[s.jsx("span",{className:"text-gray-600",children:"思维方式："}),s.jsxs("span",{className:"text-gray-900",children:[h.thinkingStyle==="logical"&&"逻辑型",h.thinkingStyle==="creative"&&"创意型",h.thinkingStyle==="analytical"&&"分析型",h.thinkingStyle==="intuitive"&&"直觉型",h.thinkingStyle==="systematic"&&"系统型"]})]}),s.jsxs("div",{children:[s.jsx("span",{className:"text-gray-600",children:"性格特征："}),s.jsxs("span",{className:"text-gray-900",children:[h.personality==="assertive"&&"果断型",h.personality==="collaborative"&&"协作型",h.personality==="diplomatic"&&"外交型",h.personality==="direct"&&"直接型",h.personality==="thoughtful"&&"深思型"]})]}),s.jsxs("div",{children:[s.jsx("span",{className:"text-gray-600",children:"管理风格："}),s.jsxs("span",{className:"text-gray-900",children:[((S=n.moderatorConfig)==null?void 0:S.managementStyle)==="strict"&&"严格型",((m=n.moderatorConfig)==null?void 0:m.managementStyle)==="flexible"&&"灵活型",((d=n.moderatorConfig)==null?void 0:d.managementStyle)==="collaborative"&&"协作型"]})]})]})]})]})}):null})(),s.jsx("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:s.jsxs("div",{className:"flex items-start gap-3",children:[s.jsx("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0",children:s.jsx(Lt,{size:16,className:"text-blue-600"})}),s.jsxs("div",{className:"flex-1",children:[s.jsx("h4",{className:"font-medium text-blue-900 mb-1",children:"主持人职责"}),s.jsxs("ul",{className:"text-sm text-blue-700 space-y-1",children:[s.jsx("li",{children:"• 实时总结讨论内容"}),s.jsx("li",{children:"• 监控话题相关性"}),s.jsx("li",{children:"• 指定发言顺序（主持人模式）"}),s.jsx("li",{children:"• 引导讨论方向"})]})]})]})}),s.jsxs("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4",children:[s.jsx("h4",{className:"font-medium text-gray-900 mb-3",children:"主持人配置"}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"总结频率"}),s.jsxs("select",{value:((x=n.moderatorConfig)==null?void 0:x.summaryFrequency)||5,onChange:h=>r(S=>{var m,d,u,p;return{...S,moderatorConfig:{...S.moderatorConfig,summaryFrequency:parseInt(h.target.value),interventionThreshold:((m=S.moderatorConfig)==null?void 0:m.interventionThreshold)||.6,managementStyle:((d=S.moderatorConfig)==null?void 0:d.managementStyle)||"flexible",autoTerminate:((u=S.moderatorConfig)==null?void 0:u.autoTerminate)||!0,maxInterventions:((p=S.moderatorConfig)==null?void 0:p.maxInterventions)||5}}}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-transparent",children:[s.jsx("option",{value:3,children:"每3条消息"}),s.jsx("option",{value:5,children:"每5条消息"}),s.jsx("option",{value:8,children:"每8条消息"}),s.jsx("option",{value:10,children:"每10条消息"})]})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"管理风格"}),s.jsxs("select",{value:((g=n.moderatorConfig)==null?void 0:g.managementStyle)||"flexible",onChange:h=>r(S=>{var m,d,u,p;return{...S,moderatorConfig:{...S.moderatorConfig,summaryFrequency:((m=S.moderatorConfig)==null?void 0:m.summaryFrequency)||5,interventionThreshold:((d=S.moderatorConfig)==null?void 0:d.interventionThreshold)||.6,managementStyle:h.target.value,autoTerminate:((u=S.moderatorConfig)==null?void 0:u.autoTerminate)||!0,maxInterventions:((p=S.moderatorConfig)==null?void 0:p.maxInterventions)||5}}}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-transparent",children:[s.jsx("option",{value:"strict",children:"严格型 - 严格控制发言顺序"}),s.jsx("option",{value:"flexible",children:"灵活型 - 适度引导讨论"}),s.jsx("option",{value:"collaborative",children:"协作型 - 鼓励自由交流"})]})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"干预阈值"}),s.jsxs("select",{value:((y=n.moderatorConfig)==null?void 0:y.interventionThreshold)||.6,onChange:h=>r(S=>{var m,d,u,p;return{...S,moderatorConfig:{...S.moderatorConfig,summaryFrequency:((m=S.moderatorConfig)==null?void 0:m.summaryFrequency)||5,interventionThreshold:parseFloat(h.target.value),managementStyle:((d=S.moderatorConfig)==null?void 0:d.managementStyle)||"flexible",autoTerminate:((u=S.moderatorConfig)==null?void 0:u.autoTerminate)||!0,maxInterventions:((p=S.moderatorConfig)==null?void 0:p.maxInterventions)||5}}}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-transparent",children:[s.jsx("option",{value:.8,children:"高敏感度 - 轻微偏题即干预"}),s.jsx("option",{value:.6,children:"中等敏感度 - 适度偏题时干预"}),s.jsx("option",{value:.4,children:"低敏感度 - 严重偏题才干预"})]})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"最大干预次数"}),s.jsxs("select",{value:((v=n.moderatorConfig)==null?void 0:v.maxInterventions)||5,onChange:h=>r(S=>{var m,d,u,p;return{...S,moderatorConfig:{...S.moderatorConfig,summaryFrequency:((m=S.moderatorConfig)==null?void 0:m.summaryFrequency)||5,interventionThreshold:((d=S.moderatorConfig)==null?void 0:d.interventionThreshold)||.6,managementStyle:((u=S.moderatorConfig)==null?void 0:u.managementStyle)||"flexible",autoTerminate:((p=S.moderatorConfig)==null?void 0:p.autoTerminate)||!0,maxInterventions:parseInt(h.target.value)}}}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-transparent",children:[s.jsx("option",{value:3,children:"3次"}),s.jsx("option",{value:5,children:"5次"}),s.jsx("option",{value:8,children:"8次"}),s.jsx("option",{value:-1,children:"不限制"})]})]})]}),s.jsx("div",{className:"mt-4",children:s.jsxs("label",{className:"flex items-center gap-2",children:[s.jsx("input",{type:"checkbox",checked:((N=n.moderatorConfig)==null?void 0:N.autoTerminate)||!0,onChange:h=>r(S=>{var m,d,u,p;return{...S,moderatorConfig:{...S.moderatorConfig,summaryFrequency:((m=S.moderatorConfig)==null?void 0:m.summaryFrequency)||5,interventionThreshold:((d=S.moderatorConfig)==null?void 0:d.interventionThreshold)||.6,managementStyle:((u=S.moderatorConfig)==null?void 0:u.managementStyle)||"flexible",autoTerminate:h.target.checked,maxInterventions:((p=S.moderatorConfig)==null?void 0:p.maxInterventions)||5}}}),className:"w-4 h-4 text-purple-600 border-gray-300 rounded focus:ring-purple-500"}),s.jsx("span",{className:"text-sm text-gray-700",children:"允许主持人自动终止讨论"})]})})]})]}),s.jsx("p",{className:"text-sm text-gray-500",children:"主持人将负责管理讨论流程，确保讨论高效有序进行"})]})]}),s.jsxs("div",{className:"border-t border-gray-200 pt-6",children:[s.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"高级设置（可选）"}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"最大消息数量"}),s.jsx("input",{type:"number",min:"10",max:"100",value:n.maxMessages||"",onChange:h=>r(S=>({...S,maxMessages:h.target.value?parseInt(h.target.value):void 0})),placeholder:"不限制",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"时间限制（分钟）"}),s.jsx("input",{type:"number",min:"5",max:"120",value:n.timeLimit||"",onChange:h=>r(S=>({...S,timeLimit:h.target.value?parseInt(h.target.value):void 0})),placeholder:"不限制",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"})]})]})]}),s.jsx("div",{className:"flex justify-center pt-6",children:s.jsxs("button",{onClick:a,disabled:!n.topic.trim()||n.selectedAgents.length<2,className:"flex items-center gap-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-xl hover:from-purple-700 hover:to-pink-700 disabled:from-gray-400 disabled:to-gray-500 disabled:cursor-not-allowed transition-all shadow-lg text-lg font-medium",children:[s.jsx(op,{size:24}),"开始讨论"]})})]})]})})})}const Bt=class Bt{static getInstance(){return Bt.instance||(Bt.instance=new Bt),Bt.instance}async callLLM(t,n){try{return await this.makeAPICall(t,n)}catch(r){throw console.error("LLM API调用失败:",r),new Error(`LLM调用失败: ${r instanceof Error?r.message:"未知错误"}`)}}async generateResponse(t,n){const r={messages:[{role:"user",content:n}],temperature:t.temperature||.7,maxTokens:t.maxTokens||500,model:t.model};return(await this.callLLM(t,r)).content}async checkSpeakingWillingness(t,n,r=[]){if(!t.llmConfig)return{willing:!0,reason:"默认愿意发言"};try{const l=this.buildWillingnessPrompt(t,n,r),i=await this.generateResponse(t.llmConfig,l);return console.log(`智能体 ${t.name} 发言意愿查询结果:`,i),this.parseWillingnessResponse(i)}catch(l){return console.error(`智能体 ${t.name} 发言意愿查询失败:`,l),{willing:!0,reason:"查询失败，默认愿意发言"}}}async generateAgentMessage(t,n,r,l=[],i=!1){const o=this.buildSystemPrompt(t,r,i),a=Ft.buildConversationHistory(t.id,n.id,{limit:10,includeOwnMessages:!0,includeOthersMessages:!0}),c={messages:[{role:"system",content:o},...a,{role:"user",content:`请基于当前讨论情况，就"${r}"这个话题发表你的观点。`}],temperature:t.llmConfig.temperature||.7,maxTokens:t.llmConfig.maxTokens||500,model:t.llmConfig.model};return(await this.callLLM(t.llmConfig,c)).content}buildSystemPrompt(t,n,r=!1){const l=r?`你是一个名为"${t.name}"的智能体，正在作为主持人主持关于"${n}"的讨论。`:`你是一个名为"${t.name}"的智能体，正在参与关于"${n}"的讨论。`,i=r?`作为主持人，你的职责包括：
1. 引导讨论方向，确保讨论围绕主题进行
2. 平衡各方观点，促进建设性对话
3. 适时总结要点，推进讨论进展
4. 维持讨论秩序，化解可能的冲突
5. 在适当时机推动达成共识`:`请根据你的专业背景和性格特征参与讨论，保持角色一致性。你的回复应该：
1. 体现你的专业知识和思维方式
2. 符合你的性格特征
3. 简洁明了，通常在100字以内
4. 针对讨论话题提供有价值的新观点`;return`${l}

你的特征：
- 专业领域：${t.expertise.join("、")}
- 思维方式：${t.thinkingStyle}
- 性格特征：${t.personality}
- 可用工具：${t.tools.join("、")}

${i}

${t.llmConfig.systemPrompt||""}`}async makeAPICall(t,n){const r=this.getAPIUrl(t),l=this.getAPIHeaders(t),i=this.formatRequestBody(t,n),o=await fetch(r,{method:"POST",headers:l,body:JSON.stringify(i)});if(!o.ok)throw new Error(`API请求失败: ${o.status} ${o.statusText}`);const a=await o.json();return this.parseResponse(t,a)}getAPIUrl(t){if(t.baseURL)return`${t.baseURL}/chat/completions`;switch(t.provider){case"openai":return"https://api.openai.com/v1/chat/completions";case"anthropic":return"https://api.anthropic.com/v1/messages";case"azure":return`${t.baseURL}/openai/deployments/${t.model}/chat/completions?api-version=2023-12-01-preview`;default:throw new Error(`不支持的提供商: ${t.provider}`)}}getAPIHeaders(t){const n={"Content-Type":"application/json"};switch(t.provider){case"openai":case"azure":case"custom":n.Authorization=`Bearer ${t.apiKey}`;break;case"anthropic":n["x-api-key"]=t.apiKey,n["anthropic-version"]="2023-06-01";break}return n}formatRequestBody(t,n){var r;switch(t.provider){case"anthropic":return{model:n.model||t.model,max_tokens:n.maxTokens||1e3,temperature:n.temperature||.7,messages:n.messages.filter(l=>l.role!=="system"),system:((r=n.messages.find(l=>l.role==="system"))==null?void 0:r.content)||""};default:return{model:n.model||t.model,messages:n.messages,temperature:n.temperature||.7,max_tokens:n.maxTokens||1e3}}}parseResponse(t,n){var r,l,i;switch(t.provider){case"anthropic":return{content:((r=n.content[0])==null?void 0:r.text)||"",usage:n.usage?{promptTokens:n.usage.input_tokens,completionTokens:n.usage.output_tokens,totalTokens:n.usage.input_tokens+n.usage.output_tokens}:void 0,model:n.model};default:return{content:((i=(l=n.choices[0])==null?void 0:l.message)==null?void 0:i.content)||"",usage:n.usage?{promptTokens:n.usage.prompt_tokens,completionTokens:n.usage.completion_tokens,totalTokens:n.usage.total_tokens}:void 0,model:n.model}}}async testLLMConfig(t){try{const n={messages:[{role:"user",content:'请回复"测试成功"'}],temperature:.1,maxTokens:10};return await this.callLLM(t,n),!0}catch(n){return console.error("LLM配置测试失败:",n),!1}}buildWillingnessPrompt(t,n,r){const i={assertive:"作为果断型性格，你倾向于主动表达明确观点",collaborative:"作为协作型性格，你倾向于在需要协调和合作时发言",diplomatic:"作为外交型性格，你倾向于在有分歧时发言调和",direct:"作为直接型性格，你倾向于直接表达观点，不避讳争议",thoughtful:"作为深思型性格，你倾向于在充分思考后发言"}[t.personality]||"",o=r.slice(-5).map(a=>`- ${a.content}`).join(`
`);return`作为智能体${t.name}，你需要判断在当前讨论情况下是否想要发言。

你的特征：
- 专业领域：${t.expertise.join("、")}
- 思维方式：${t.thinkingStyle}
- 性格特征：${t.personality}

${i}

当前讨论主题：${n.topic}

最近的讨论内容：
${o}

请基于以下因素判断是否想要发言：
1. 这个话题是否与你的专业领域相关？
2. 你是否有新的观点或有价值的补充？
3. 根据你的性格特征，现在是否是合适的发言时机？
4. 是否有需要澄清或纠正的内容？

请回答：是否想要发言（是/否），并简要说明理由（不超过30字）。
格式：是/否 - 理由`}parseWillingnessResponse(t){try{const n=t.trim(),r=n.match(/^(是|否)\s*[-–—]\s*(.+)$/);return r?{willing:r[1]==="是",reason:r[2].trim()}:n.startsWith("是")?{willing:!0,reason:n.replace(/^是\s*[-–—]?\s*/,"")||"愿意发言"}:n.startsWith("否")?{willing:!1,reason:n.replace(/^否\s*[-–—]?\s*/,"")||"不愿意发言"}:{willing:!0,reason:"格式解析失败，默认愿意发言"}}catch(n){return console.error("解析发言意愿响应失败:",n),{willing:!0,reason:"解析失败，默认愿意发言"}}}};ge(Bt,"instance");let Si=Bt;const dt=Si.getInstance();async function bp(e,t,n,r=[],l=!1){if(!e.llmConfig)throw new Error(`智能体 ${e.name} 未配置LLM，无法生成消息`);try{return await dt.generateAgentMessage(e,t,n,r,l)}catch(i){throw console.error(`智能体 ${e.name} 的LLM调用失败:`,i),new Error(`智能体 ${e.name} 的LLM调用失败: ${i instanceof Error?i.message:"未知错误"}`)}}function Cp(e,t){if(e.length<3)return 0;const n=e.slice(-10),r=n.filter(g=>g.type==="agreement").length/n.length,l=n.filter(g=>g.type==="disagreement").length/n.length,i=n.filter(g=>g.type==="question").length/n.length,o=new Map;n.forEach(g=>{o.set(g.agentId,(o.get(g.agentId)||0)+1)});const a=o.size,c=t.filter(g=>g.isActive).length,f=a/c;let x=0;return x+=r*40,x+=(1-l)*30,x+=i>.1&&i<.3?15:0,x+=f*15,Math.min(100,Math.max(0,x))}function Ep(e,t){const r=e.slice(-15).filter(i=>i.type==="statement"||i.type==="agreement");if(r.length===0)return`关于"${t}"，参与者需要更多时间来达成共识。`;const l=Lp(r.map(i=>i.content));return`经过充分讨论，大家就"${t}"达成了共识：${l.slice(0,3).join("、")}是关键要素，需要重点关注和实施。`}function Lp(e){return["技术创新","用户体验","市场需求","成本控制","时间规划","质量保证","团队合作","数据分析"].filter(()=>Math.random()>.6).slice(0,5)}function ss(e,t,n){if(e.length===0)return null;if(n==="moderator"){const r=t.slice(-e.length).map(i=>i.agentId),l=e.filter(i=>!r.includes(i));return l.length>0?l[0]:e[0]}else{const r=t.slice(-10),l=new Map;e.forEach(c=>l.set(c,0)),r.forEach(c=>{e.includes(c.agentId)&&l.set(c.agentId,(l.get(c.agentId)||0)+1)});const o=[...l.entries()].sort(([,c],[,f])=>c-f).slice(0,Math.ceil(e.length/2));return o[Math.floor(Math.random()*o.length)][0]}}async function Dp(e,t,n){if(e.length===0)return{speakerId:null,willingnessResults:[]};const r=[],l=n.slice(-5);for(const a of e)try{const c=await dt.checkSpeakingWillingness(a,t,l);r.push({agentId:a.id,willing:c.willing,reason:c.reason})}catch(c){console.error(`查询智能体 ${a.name} 发言意愿失败:`,c),r.push({agentId:a.id,willing:!0,reason:"查询失败，默认愿意发言"})}const i=r.filter(a=>a.willing).map(a=>a.agentId);return i.length===0?{speakerId:null,willingnessResults:r}:{speakerId:ss(i,n,"free"),willingnessResults:r}}async function Mp(e,t,n){if(!e.llmConfig)throw new Error(`主持人 ${e.name} 未配置LLM，无法生成总结`);try{const r=`作为讨论主持人，请对以下最近的讨论内容进行简洁总结：

讨论主题：${t.topic}
最近消息：
${n.map(l=>`- ${l.content}`).join(`
`)}

请提供一个简洁的总结，突出关键观点和进展：`;return await dt.generateResponse(e.llmConfig,r)}catch(r){return console.error("主持人总结生成失败:",r),`总结生成失败：${r instanceof Error?r.message:"未知错误"}`}}async function Tp(e,t,n){if(!e.llmConfig)return .8;try{const r=`作为讨论主持人，请评估以下讨论内容与主题的相关性：

讨论主题：${t.topic}
最近消息：
${n.map(o=>`- ${o.content}`).join(`
`)}

请给出相关性评分（0-1之间的数字，1表示完全相关，0表示完全无关）。
只需要返回数字，不需要解释：`,l=await dt.generateResponse(e.llmConfig,r),i=parseFloat(l.trim());return isNaN(i)?.8:Math.max(0,Math.min(1,i))}catch(r){return console.error("话题相关性计算失败:",r),.8}}async function Ap(e,t,n,r){if(!e.llmConfig||n.length===0)return ss(n.map(l=>l.id),r,t.mode);try{const l=n.map(f=>`${f.name}（专业：${f.expertise.join("、")}，思维：${f.thinkingStyle}）`).join(`
`),i=`作为讨论主持人，请根据以下信息选择下一个最适合发言的参与者：

讨论主题：${t.topic}
参与者信息：
${l}

最近讨论内容：
${r.slice(-5).map(f=>{const x=n.find(g=>g.id===f.agentId);return`${(x==null?void 0:x.name)||"未知"}: ${f.content}`}).join(`
`)}

请选择最适合继续这个话题的参与者，只需要返回参与者的名字：`,a=(await dt.generateResponse(e.llmConfig,i)).trim(),c=n.find(f=>f.name===a);return(c==null?void 0:c.id)||ss(n.map(f=>f.id),r,t.mode)}catch(l){return console.error("智能发言人选择失败:",l),ss(n.map(i=>i.id),r,t.mode)}}async function Ip(e,t,n){if(!e.llmConfig)return{off_topic:"让我们回到主题上来，继续讨论相关内容。",low_activity:"大家可以分享更多想法，让讨论更加活跃。",conflict_resolution:"我们来总结一下不同的观点，寻找共同点。",summary_request:"让我总结一下到目前为止的讨论要点。"}[n];try{const r={off_topic:`作为讨论主持人，发现讨论偏离了主题"${t.topic}"。请生成一段引导语，礼貌地将讨论拉回正轨。`,low_activity:"作为讨论主持人，发现讨论活跃度较低。请生成一段鼓励性的引导语，激发参与者的讨论热情。",conflict_resolution:"作为讨论主持人，发现参与者之间存在分歧。请生成一段中性的引导语，帮助化解冲突并推进讨论。",summary_request:"作为讨论主持人，需要对当前讨论进行阶段性总结。请生成一段总结性的引导语。"};return(await dt.generateResponse(e.llmConfig,r[n])).trim()}catch(r){return console.error("主持人引导语生成失败:",r),"让我们继续讨论，保持专注和建设性。"}}async function Pp(e,t,n){if(t.consensusScore>85)return{shouldTerminate:!0,reason:"达成共识"};if(t.moderatorInterventions>=n.maxInterventions&&n.maxInterventions>0)return{shouldTerminate:!0,reason:"主持人终止"};if(!n.autoTerminate||!e.llmConfig)return{shouldTerminate:!1};try{const r=t.messages.slice(-10),l=`作为讨论主持人，请判断以下讨论是否应该结束：

讨论主题：${t.topic}
当前共识度：${t.consensusScore}%
话题相关性：${t.topicRelevanceScore}
干预次数：${t.moderatorInterventions}

最近讨论内容：
${r.map(a=>`- ${a.content}`).join(`
`)}

请判断是否应该结束讨论，只需要回答"是"或"否"：`,o=(await dt.generateResponse(e.llmConfig,l)).trim().toLowerCase().includes("是");return{shouldTerminate:o,reason:o?"主持人终止":void 0}}catch(r){return console.error("讨论终止判断失败:",r),{shouldTerminate:!1}}}async function _p(e,t,n){var r,l;if(!e.llmConfig)return{action:"terminate",message:"所有参与者都不愿继续发言，讨论结束。"};try{const i=n.filter(g=>!g.willing).map(g=>`- ${g.reason}`).join(`
`),o=`作为讨论主持人，当前所有参与者都不愿意发言。

讨论主题：${t.topic}
当前共识度：${t.consensusScore}%
消息总数：${t.messages.length}

参与者不发言的原因：
${i}

最近的讨论内容：
${t.messages.slice(-5).map(g=>`- ${g.content}`).join(`
`)}

请分析当前讨论状态，判断是否应该：
1. 结束讨论并进行总结
2. 提出新的问题或角度来激发继续讨论

请回答格式：
行动：结束/继续
消息：[你的总结或新问题]`,c=(await dt.generateResponse(e.llmConfig,o)).trim().split(`
`);let f="terminate",x="讨论结束。";for(const g of c)if(g.includes("行动：")){const y=(r=g.split("行动：")[1])==null?void 0:r.trim();y!=null&&y.includes("继续")&&(f="continue")}else g.includes("消息：")&&(x=((l=g.split("消息：")[1])==null?void 0:l.trim())||x);return{action:f,message:x}}catch(i){return console.error("主持人处理无人发言失败:",i),{action:"terminate",message:"讨论遇到问题，现在结束。"}}}function zp(){var rn,E;const{state:e,dispatch:t,endDiscussion:n,sendMessage:r,setCurrentDiscussion:l}=Pt(),[i,o]=D.useState(!1),[a,c]=D.useState(null),[f,x]=D.useState(!1),[g,y]=D.useState({totalMessages:0,consensusScore:0,activeTime:0}),[v,N]=D.useState(""),[h,S]=D.useState(1),[m,d]=D.useState(0),u=D.useRef(null),p=D.useRef(null),j=D.useRef(null),{currentDiscussion:w}=e;D.useEffect(()=>(w&&w.status==="active"&&e.isDiscussionActive?(console.log("启动讨论模拟 - 讨论ID:",w.id,"状态:",w.status,"活跃:",e.isDiscussionActive),o(!0),M()):(console.log("停止讨论模拟 - 讨论ID:",w==null?void 0:w.id,"状态:",w==null?void 0:w.status,"活跃:",e.isDiscussionActive),o(!1),j.current&&(clearTimeout(j.current),j.current=null),p.current&&(clearInterval(p.current),p.current=null)),()=>{p.current&&clearInterval(p.current),j.current&&clearTimeout(j.current)}),[w,e.isDiscussionActive]),D.useEffect(()=>{i||j.current&&(clearTimeout(j.current),j.current=null)},[i]),D.useEffect(()=>{var k;(k=u.current)==null||k.scrollIntoView({behavior:"smooth"})},[w==null?void 0:w.messages]),D.useEffect(()=>{if(w){const k=Cp(w.messages,e.agents);let C;if(e.isDiscussionActive)C=Math.floor((Date.now()-new Date(w.createdAt).getTime())/1e3);else{const A=w.messages[w.messages.length-1];A?C=Math.floor((new Date(A.timestamp).getTime()-new Date(w.createdAt).getTime())/1e3):C=0}if(y({totalMessages:w.messages.length,consensusScore:k,activeTime:C}),e.isDiscussionActive&&(t({type:"UPDATE_CONSENSUS",payload:{consensusScore:k}}),k>80&&w.status==="active")){const A=Ep(w.messages,w.topic);t({type:"UPDATE_CONSENSUS",payload:{consensusScore:k,consensus:A}}),o(!1),j.current&&(clearTimeout(j.current),j.current=null),setTimeout(async()=>{await O("达成共识")},2e3)}}},[w==null?void 0:w.messages]),D.useEffect(()=>{if(!e.isDiscussionActive&&e.currentDiscussion===null&&a===null){if(e.allDiscussions.length>0){const k=e.allDiscussions[0];k.status==="ended"&&c(k)}}else e.isDiscussionActive&&e.currentDiscussion&&(c(null),x(!1))},[e.isDiscussionActive,e.currentDiscussion,e.allDiscussions,a]);const M=()=>{if(!w||w.status!=="active"||!e.isDiscussionActive){console.log("无法启动模拟 - 讨论状态:",w==null?void 0:w.status,"活跃状态:",e.isDiscussionActive);return}console.log("开始讨论模拟 - 讨论ID:",w.id);const k=w.participants,C=e.agents.filter(I=>k.includes(I.id)),A=w.moderatorId?e.agents.find(I=>I.id===w.moderatorId):null,z=async()=>{var $t,jo,wo,No;if(!e.currentDiscussion||e.currentDiscussion.status!=="active"||!e.isDiscussionActive){console.log("停止模拟消息 - 状态检查失败:",{hasDiscussion:!!e.currentDiscussion,status:($t=e.currentDiscussion)==null?void 0:$t.status,isActive:e.isDiscussionActive}),o(!1),j.current&&(clearTimeout(j.current),j.current=null);return}const I=e.currentDiscussion;if(A&&A.moderatorConfig){const oe=A.moderatorConfig;if(I.messages.length-m>=oe.summaryFrequency)try{const ee=await Mp(A,I,I.messages.slice(-oe.summaryFrequency));N(ee),d(I.messages.length);const sn=[...I.moderatorSummaries,ee];t({type:"UPDATE_CONSENSUS",payload:{consensusScore:I.consensusScore,moderatorSummaries:sn}})}catch(ee){console.error("主持人总结生成失败:",ee)}if(I.messages.length>0)try{const ee=await Tp(A,I,I.messages.slice(-3));if(S(ee),ee<oe.interventionThreshold){const sn=(I.moderatorInterventions||0)+1;if(sn>=oe.maxInterventions&&oe.maxInterventions>0){console.log("达到最大干预次数，主持人终止讨论"),await O("偏离话题");return}const ad=await Ip(A,I,"off_topic");if(!e.currentDiscussion||e.currentDiscussion.status!=="active"||!e.isDiscussionActive){console.log("主持人干预生成完成但讨论已结束，取消发送");return}if(r(ad,A.id,"statement"),t({type:"UPDATE_CONSENSUS",payload:{consensusScore:I.consensusScore,moderatorInterventions:sn}}),e.isDiscussionActive&&((jo=e.currentDiscussion)==null?void 0:jo.status)==="active"&&i){const cd=Math.random()*3e3+2e3;j.current=setTimeout(z,cd)}else o(!1);return}}catch(ee){console.error("话题相关性检查失败:",ee)}try{const ee=await Pp(A,I,oe);if(ee.shouldTerminate){console.log("主持人决定终止讨论:",ee.reason),await O(ee.reason||"主持人终止");return}}catch(ee){console.error("讨论终止检查失败:",ee)}}let ie=null,H=[];if(A&&I.mode==="moderator")ie=await Ap(A,I,C,I.messages);else{const oe=await Dp(C,I,I.messages);if(ie=oe.speakerId,H=oe.willingnessResults,!ie&&A){console.log("没有智能体愿意发言，主持人介入处理");try{const Ee=await _p(A,I,H);if(Ee.action==="terminate"){console.log("主持人决定结束讨论:",Ee.message),await O("无人发言");return}else{if(console.log("主持人提出新问题继续讨论:",Ee.message),!e.currentDiscussion||e.currentDiscussion.status!=="active"||!e.isDiscussionActive){console.log("主持人消息生成完成但讨论已结束，取消发送");return}if(r(Ee.message,A.id,"statement"),e.isDiscussionActive&&((wo=e.currentDiscussion)==null?void 0:wo.status)==="active"&&i){const ee=Math.random()*3e3+2e3;j.current=setTimeout(z,ee)}else o(!1);return}}catch(Ee){console.error("主持人处理无人发言失败:",Ee),await O("处理异常");return}}else if(!ie){console.log("没有主持人且无人愿意发言，讨论结束"),await O("无人发言");return}}if(ie){const oe=C.find(Ee=>Ee.id===ie);if(oe)try{const Ee=!!(A&&oe.id===A.id),ee=await bp(oe,I,I.topic,I.messages.slice(-5),Ee);if(!e.currentDiscussion||e.currentDiscussion.status!=="active"||!e.isDiscussionActive){console.log("消息生成完成但讨论已结束，取消发送:",oe.name);return}const sn=T(ee);r(ee,oe.id,sn)}catch(Ee){console.error("生成消息失败:",Ee)}}if(e.isDiscussionActive&&((No=e.currentDiscussion)==null?void 0:No.status)==="active"&&i){const oe=Math.random()*3e3+2e3;j.current=setTimeout(z,oe)}else o(!1),j.current&&(clearTimeout(j.current),j.current=null)},K=Math.random()*2e3+1e3;j.current=setTimeout(z,K)},T=k=>k.includes("我赞同")||k.includes("我同意")||k.includes("这个想法很好")?"agreement":k.includes("但是")||k.includes("我认为")||k.includes("不同的看法")?"disagreement":k.includes("？")||k.includes("我们")||k.includes("如何")?"question":"statement",O=async k=>{if(!w)return;const C=w.moderatorSummaries.length>0?w.moderatorSummaries[w.moderatorSummaries.length-1]:v||"";c({...w,status:"ended",endReason:k,finalModeratorSummary:C,endedAt:new Date}),n(k)},_=async()=>{console.log("用户点击结束讨论按钮"),o(!1),j.current&&(clearTimeout(j.current),j.current=null),p.current&&(clearInterval(p.current),p.current=null),await O("手动终止"),console.log("讨论结束处理完成")},we=k=>{const C=Math.floor(k/60),A=k%60;return`${C}:${A.toString().padStart(2,"0")}`},_t=k=>{switch(k){case"达成共识":return"bg-green-50 border-green-200 text-green-800";case"手动终止":return"bg-blue-50 border-blue-200 text-blue-800";case"偏离话题":return"bg-orange-50 border-orange-200 text-orange-800";case"主持人终止":return"bg-purple-50 border-purple-200 text-purple-800";case"时间超限":return"bg-yellow-50 border-yellow-200 text-yellow-800";case"消息数量达到上限":return"bg-indigo-50 border-indigo-200 text-indigo-800";default:return"bg-gray-50 border-gray-200 text-gray-800"}},zt=k=>{switch(k){case"达成共识":return s.jsx(tt,{size:16,className:"text-green-600"});case"手动终止":return s.jsx(_a,{size:16,className:"text-blue-600"});case"偏离话题":return s.jsx(Ms,{size:16,className:"text-orange-600"});case"主持人终止":return s.jsx(fp,{size:16,className:"text-purple-600"});case"时间超限":return s.jsx(Ts,{size:16,className:"text-yellow-600"});case"消息数量达到上限":return s.jsx(dp,{size:16,className:"text-indigo-600"});default:return s.jsx(Ts,{size:16,className:"text-gray-600"})}},Cr=k=>{switch(k){case"达成共识":return"参与者达成共识";case"手动终止":return"用户手动结束";case"偏离话题":return"讨论偏离主题";case"主持人终止":return"主持人主动终止";case"时间超限":return"讨论时间超限";case"消息数量达到上限":return"消息数量达到上限";default:return k||"未知原因"}},Er=k=>{switch(k){case"达成共识":return"讨论参与者在话题上达成了足够的共识（共识度超过85%），系统自动结束了讨论";case"手动终止":return'用户主动点击"结束讨论"按钮终止了讨论';case"偏离话题":return"主持人检测到讨论内容偏离了原定话题，达到干预上限后终止讨论";case"主持人终止":return"主持人基于讨论情况（如活跃度、质量等）判断应该结束讨论";case"时间超限":return"讨论时间超过了预设的时间限制";case"消息数量达到上限":return"讨论消息数量达到了预设的上限";default:return"讨论因其他原因结束"}};if(!w){if(a){const k=e.agents.filter(C=>a.participants.includes(C.id));return s.jsx("div",{className:"h-full bg-gradient-to-br from-green-50 to-blue-50 w-full overflow-y-auto",children:s.jsxs("div",{className:"max-w-7xl mx-auto p-6 space-y-6",children:[s.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-8 text-center",children:[s.jsx(tt,{size:64,className:"text-green-500 mx-auto mb-4"}),s.jsx("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"讨论已结束"}),s.jsxs("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6",children:[s.jsx("h3",{className:"text-lg font-semibold text-blue-800 mb-2",children:"讨论话题"}),s.jsx("p",{className:"text-2xl font-bold text-blue-900",children:a.topic})]}),a.endReason&&s.jsxs("div",{className:`border rounded-lg p-4 mb-6 ${_t(a.endReason)}`,children:[s.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[zt(a.endReason),s.jsx("h4",{className:"text-sm font-semibold",children:"讨论结束原因"})]}),s.jsx("p",{className:"text-lg font-medium",children:Cr(a.endReason)}),Er(a.endReason)&&s.jsx("p",{className:"text-sm mt-2 opacity-80",children:Er(a.endReason)}),s.jsxs("div",{className:"mt-3 pt-3 border-t border-current border-opacity-20",children:[s.jsxs("div",{className:"flex items-center justify-between text-sm opacity-75",children:[s.jsxs("span",{children:["开始时间：",new Date(a.createdAt).toLocaleString()]}),a.endedAt&&s.jsxs("span",{children:["结束时间：",new Date(a.endedAt).toLocaleString()]})]}),a.endedAt&&s.jsx("div",{className:"text-center mt-2",children:s.jsxs("span",{className:"text-sm font-medium",children:["总时长：",Math.floor((new Date(a.endedAt).getTime()-new Date(a.createdAt).getTime())/6e4)," 分钟"]})})]})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[s.jsxs("div",{className:"bg-blue-50 rounded-lg p-4",children:[s.jsx("div",{className:"text-2xl font-bold text-blue-600",children:a.messages.length}),s.jsx("div",{className:"text-sm text-blue-800",children:"总消息数"})]}),s.jsxs("div",{className:"bg-green-50 rounded-lg p-4",children:[s.jsxs("div",{className:"text-2xl font-bold text-green-600",children:[a.consensusScore,"%"]}),s.jsx("div",{className:"text-sm text-green-800",children:"共识度"})]}),s.jsxs("div",{className:"bg-purple-50 rounded-lg p-4",children:[s.jsx("div",{className:"text-2xl font-bold text-purple-600",children:a.participants.length}),s.jsx("div",{className:"text-sm text-purple-800",children:"参与者"})]}),s.jsxs("div",{className:"bg-orange-50 rounded-lg p-4",children:[s.jsx("div",{className:"text-2xl font-bold text-orange-600",children:Math.floor((new Date().getTime()-new Date(a.createdAt).getTime())/6e4)}),s.jsx("div",{className:"text-sm text-orange-800",children:"讨论时长(分钟)"})]})]})]}),a.consensus&&s.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-8",children:[s.jsxs("div",{className:"flex items-center gap-3 mb-6",children:[s.jsx(tt,{size:32,className:"text-green-600"}),s.jsx("h3",{className:"text-2xl font-bold text-gray-900",children:"最终结论"})]}),s.jsx("div",{className:"bg-green-50 border border-green-200 rounded-lg p-6",children:s.jsx("p",{className:"text-gray-800 text-lg leading-relaxed",children:a.consensus})})]}),a.moderatorId&&s.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-8",children:[s.jsxs("div",{className:"flex items-center gap-3 mb-6",children:[s.jsx(st,{size:32,className:"text-purple-600"}),s.jsx("h3",{className:"text-2xl font-bold text-gray-900",children:"主持人最后一次总结"}),s.jsx("span",{className:"bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm font-medium",children:(()=>{const C=e.agents.find(A=>A.id===a.moderatorId);return C?C.name:"主持人"})()})]}),s.jsxs("div",{className:"bg-purple-50 border border-purple-200 rounded-lg p-6",children:[s.jsxs("div",{className:"flex items-center gap-2 mb-3",children:[s.jsx("div",{className:"w-2 h-2 bg-purple-500 rounded-full"}),s.jsx("span",{className:"text-sm text-purple-700 font-medium",children:"讨论过程中的最后一次总结"})]}),a.finalModeratorSummary?s.jsx("p",{className:"text-gray-800 text-lg leading-relaxed whitespace-pre-wrap",children:a.finalModeratorSummary}):s.jsxs("div",{className:"text-center py-4",children:[s.jsx("p",{className:"text-gray-500 text-base",children:"主持人在讨论过程中未生成总结"}),s.jsx("p",{className:"text-gray-400 text-sm mt-2",children:"可能是因为讨论时间较短或消息数量未达到总结阈值"})]})]})]}),s.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-8",children:[s.jsxs("div",{className:"flex items-center gap-3 mb-6",children:[s.jsx(kl,{size:32,className:"text-indigo-600"}),s.jsx("h3",{className:"text-2xl font-bold text-gray-900",children:"消息类型分析"})]}),(()=>{const C={statement:{count:0,label:"陈述",color:"gray",icon:Be},question:{count:0,label:"提问",color:"blue",icon:nd},agreement:{count:0,label:"赞同",color:"green",icon:ld},disagreement:{count:0,label:"反对",color:"red",icon:sd}};a.messages.forEach(z=>{C[z.type]&&C[z.type].count++});const A=a.messages.length;return s.jsx("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:Object.entries(C).map(([z,K])=>{const I=A>0?Math.round(K.count/A*100):0,ie=K.icon;return s.jsxs("div",{className:`bg-${K.color}-50 rounded-lg p-4 text-center`,children:[s.jsx(ie,{size:24,className:`text-${K.color}-600 mx-auto mb-2`}),s.jsx("div",{className:`text-2xl font-bold text-${K.color}-600`,children:K.count}),s.jsx("div",{className:`text-sm text-${K.color}-800 font-medium`,children:K.label}),s.jsxs("div",{className:`text-xs text-${K.color}-600 mt-1`,children:[I,"%"]})]},z)})})})()]}),s.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-8",children:[s.jsxs("div",{className:"flex items-center gap-3 mb-6",children:[s.jsx(st,{size:32,className:"text-blue-600"}),s.jsx("h3",{className:"text-2xl font-bold text-gray-900",children:"参与者表现"})]}),s.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:k.map(C=>{const A=a.messages.filter(H=>H.agentId===C.id).length,z=a.messages.filter(H=>H.agentId===C.id&&H.type==="agreement").length,K=a.messages.filter(H=>H.agentId===C.id&&H.type==="disagreement").length,I=a.messages.filter(H=>H.agentId===C.id&&H.type==="question").length,ie=a.messages.filter(H=>H.agentId===C.id&&H.type==="statement").length;return s.jsxs("div",{className:"bg-gray-50 rounded-lg p-4",children:[s.jsxs("div",{className:"flex items-center gap-3 mb-3",children:[s.jsx("img",{src:C.avatar,alt:C.name,className:"w-12 h-12 rounded-full object-cover"}),s.jsxs("div",{children:[s.jsx("h4",{className:"font-semibold text-gray-900",children:C.name}),s.jsx("p",{className:"text-sm text-gray-500",children:C.expertise.slice(0,2).join("、")})]})]}),s.jsxs("div",{className:"space-y-2 text-sm",children:[s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-gray-600",children:"总发言"}),s.jsx("span",{className:"font-medium",children:A})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-gray-600",children:"陈述"}),s.jsx("span",{className:"font-medium text-gray-600",children:ie})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-gray-600",children:"提问"}),s.jsx("span",{className:"font-medium text-blue-600",children:I})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-gray-600",children:"赞同"}),s.jsx("span",{className:"font-medium text-green-600",children:z})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-gray-600",children:"反对"}),s.jsx("span",{className:"font-medium text-red-600",children:K})]})]})]},C.id)})})]}),s.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-8",children:[s.jsxs("div",{className:"flex items-center justify-between mb-6",children:[s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx(Be,{size:32,className:"text-purple-600"}),s.jsx("h3",{className:"text-2xl font-bold text-gray-900",children:"完整消息记录"}),s.jsxs("span",{className:"bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm font-medium",children:[a.messages.length," 条消息"]})]}),s.jsxs("button",{onClick:()=>x(!f),className:"flex items-center gap-2 px-4 py-2 bg-purple-100 text-purple-700 rounded-lg hover:bg-purple-200 transition-colors",children:[f?"收起消息":"展开消息",s.jsx(kl,{size:16})]})]}),f?s.jsx("div",{className:"max-h-96 overflow-y-auto space-y-4 border border-gray-200 rounded-lg p-4",children:a.messages.map(C=>{const A=k.find(z=>z.id===C.agentId);return s.jsx(Oa,{message:C,agent:A},C.id)})}):s.jsxs("div",{className:"border border-gray-200 rounded-lg p-6 text-center",children:[s.jsx("p",{className:"text-gray-500 mb-4",children:'点击"展开消息"查看完整的讨论记录'}),s.jsxs("div",{className:"flex justify-center gap-4 text-sm text-gray-400",children:[s.jsxs("span",{children:["首条消息：",new Date((rn=a.messages[0])==null?void 0:rn.timestamp).toLocaleString()]}),s.jsx("span",{children:"•"}),s.jsxs("span",{children:["末条消息：",new Date((E=a.messages[a.messages.length-1])==null?void 0:E.timestamp).toLocaleString()]})]})]})]}),s.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-8",children:[s.jsxs("div",{className:"flex gap-4 justify-center",children:[s.jsx("button",{onClick:()=>{o(!1),j.current&&(clearTimeout(j.current),j.current=null),p.current&&(clearInterval(p.current),p.current=null),l(a),c(null),x(!1)},className:"px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium",children:"回到讨论室"}),s.jsx("button",{onClick:()=>{},className:"px-8 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors font-medium",children:"查看历史"}),s.jsx("button",{onClick:()=>{const C={topic:a.topic,participants:k.map(I=>I.name),messages:a.messages.map(I=>{var ie;return{speaker:((ie=k.find(H=>H.id===I.agentId))==null?void 0:ie.name)||"Unknown",content:I.content,type:I.type,timestamp:new Date(I.timestamp).toLocaleString()}}),consensus:a.consensus,consensusScore:a.consensusScore,duration:Math.floor((new Date().getTime()-new Date(a.createdAt).getTime())/6e4)},A=new Blob([JSON.stringify(C,null,2)],{type:"application/json"}),z=URL.createObjectURL(A),K=document.createElement("a");K.href=z,K.download=`讨论记录_${a.topic}_${new Date().toISOString().split("T")[0]}.json`,document.body.appendChild(K),K.click(),document.body.removeChild(K),URL.revokeObjectURL(z)},className:"px-8 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium",children:"导出记录"})]}),s.jsx("div",{className:"mt-6 text-center",children:s.jsx("p",{className:"text-sm text-gray-500",children:'您可以通过导航栏的"创建讨论"按钮开始新的讨论'})})]})]})})}return s.jsx("div",{className:"h-full bg-gradient-to-br from-gray-50 to-blue-50 w-full overflow-y-auto",children:s.jsx("div",{className:"discussion-container",children:s.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-8 text-center max-w-2xl",children:[s.jsx(Be,{size:64,className:"text-gray-400 mx-auto mb-4"}),s.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"没有进行中的讨论"}),s.jsx("p",{className:"text-gray-600",children:"请先创建一个新的讨论来开始智能体对话。"})]})})})}const Rt=e.agents.filter(k=>w.participants.includes(k.id));return s.jsx("div",{className:"h-screen bg-gradient-to-br from-blue-50 to-indigo-50 w-full overflow-hidden pt-16",children:s.jsx("div",{className:"discussion-container h-full",children:s.jsxs("div",{className:"flex gap-6 h-full max-w-none w-full",children:[w.moderatorId&&(()=>{const k=e.agents.find(C=>C.id===w.moderatorId);return k?s.jsx("div",{className:"fixed-size-sidebar flex-shrink-0",children:s.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-6 h-fit",children:[s.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[s.jsx("div",{className:"w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center",children:s.jsx(st,{size:16,className:"text-purple-600"})}),s.jsx("h3",{className:"font-semibold text-gray-900",children:"主持人"})]}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx("img",{src:k.avatar,alt:k.name,className:"w-12 h-12 rounded-full object-cover"}),s.jsxs("div",{children:[s.jsx("h4",{className:"font-medium text-gray-900",children:k.name}),s.jsx("p",{className:"text-sm text-gray-500",children:k.expertise.slice(0,2).join("、")})]})]}),k.moderatorConfig&&s.jsxs("div",{className:"bg-purple-50 border border-purple-200 rounded-lg p-3",children:[s.jsx("h5",{className:"text-sm font-medium text-purple-900 mb-2",children:"管理风格"}),s.jsx("p",{className:"text-sm text-purple-700",children:k.moderatorConfig.managementStyle==="strict"?"严格型":k.moderatorConfig.managementStyle==="flexible"?"灵活型":"协作型"})]}),s.jsxs("div",{className:"space-y-2",children:[s.jsxs("div",{className:"flex items-center justify-between text-sm",children:[s.jsx("span",{className:"text-gray-600",children:"话题相关性"}),s.jsxs("span",{className:`font-medium ${h>.8?"text-green-600":h>.6?"text-yellow-600":"text-red-600"}`,children:[Math.round(h*100),"%"]})]}),s.jsx("div",{className:"w-full h-2 bg-gray-200 rounded-full overflow-hidden",children:s.jsx("div",{className:`h-full transition-all duration-500 ${h>.8?"bg-green-500":h>.6?"bg-yellow-500":"bg-red-500"}`,style:{width:`${h*100}%`}})})]}),v&&s.jsxs("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-3",children:[s.jsx("h5",{className:"text-sm font-medium text-blue-900 mb-2",children:"最新总结"}),s.jsx("p",{className:"text-sm text-blue-700",children:v})]}),s.jsxs("div",{className:"flex items-center justify-between text-sm",children:[s.jsx("span",{className:"text-gray-600",children:"干预次数"}),s.jsx("span",{className:"font-medium text-gray-900",children:w.moderatorInterventions||0})]})]})]})}):null})(),s.jsx("div",{className:"fixed-size-discussion flex-shrink-0 h-[70vh]",children:s.jsxs("div",{className:"bg-white rounded-xl shadow-lg overflow-hidden h-full flex flex-col",children:[s.jsxs("div",{className:`text-white p-6 ${e.isDiscussionActive?"bg-gradient-to-r from-blue-600 to-indigo-600":"bg-gradient-to-r from-gray-600 to-gray-700"}`,children:[s.jsxs("div",{className:"flex items-center justify-between mb-4",children:[s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx(Be,{size:32}),s.jsxs("div",{children:[s.jsx("h1",{className:"text-2xl font-bold",children:e.isDiscussionActive?"讨论进行中":"历史讨论记录"}),s.jsx("p",{className:e.isDiscussionActive?"text-blue-100":"text-gray-100",children:w.mode==="free"?"自由讨论模式":"主持人模式"})]})]}),s.jsx("div",{className:"flex items-center gap-4",children:e.isDiscussionActive?s.jsxs(s.Fragment,{children:[s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"text-2xl font-bold",children:we(g.activeTime)}),s.jsx("div",{className:"text-sm text-blue-100",children:"讨论时长"})]}),s.jsxs("button",{onClick:_,className:"flex items-center gap-2 bg-red-500 hover:bg-red-600 px-4 py-2 rounded-lg transition-colors",children:[s.jsx(_a,{size:20}),"结束讨论"]})]}):s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"text-sm text-gray-100",children:"创建时间"}),s.jsx("div",{className:"text-lg font-bold",children:new Date(w.createdAt).toLocaleString()})]})})]}),s.jsxs("div",{className:"bg-white bg-opacity-20 rounded-lg p-4",children:[s.jsx("h3",{className:"font-semibold mb-2",children:"讨论话题"}),s.jsx("p",{className:e.isDiscussionActive?"text-blue-50":"text-gray-50",children:w.topic})]})]}),s.jsxs("div",{className:"h-[calc(100%-180px)] overflow-y-auto p-6 space-y-4",children:[w.messages.map(k=>s.jsx(Oa,{message:k,agent:Rt.find(C=>C.id===k.agentId)},k.id)),i&&e.isDiscussionActive&&s.jsxs("div",{className:"flex items-center gap-3 text-gray-500",children:[s.jsx("div",{className:"animate-spin w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full"}),s.jsx("span",{children:"智能体正在思考中..."})]}),s.jsx("div",{ref:u})]})]})}),s.jsxs("div",{className:"space-y-6 fixed-size-sidebar",children:[s.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-6",children:[s.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[s.jsx(id,{size:24,className:"text-green-600"}),s.jsx("h3",{className:"font-semibold text-gray-900",children:"共识度"})]}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"relative",children:[s.jsx("div",{className:"w-full h-4 bg-gray-200 rounded-full overflow-hidden",children:s.jsx("div",{className:`h-full transition-all duration-500 ${g.consensusScore>80?"bg-green-500":g.consensusScore>60?"bg-yellow-500":"bg-red-500"}`,style:{width:`${g.consensusScore}%`}})}),s.jsxs("div",{className:"text-center mt-2 font-bold text-2xl",children:[Math.round(g.consensusScore),"%"]})]}),w.status==="consensus"&&s.jsxs("div",{className:"flex items-center gap-2 bg-green-50 text-green-800 p-3 rounded-lg",children:[s.jsx(tt,{size:20}),s.jsx("span",{className:"font-medium",children:"已达成共识！"})]})]})]}),s.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-6",children:[s.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[s.jsx(st,{size:24,className:"text-blue-600"}),s.jsx("h3",{className:"font-semibold text-gray-900",children:"参与者"})]}),s.jsx("div",{className:"space-y-3",children:Rt.filter(k=>k.id!==w.moderatorId).map(k=>{const C=w.messages.filter(z=>z.agentId===k.id).length,A=w.messages.slice().reverse().find(z=>z.agentId===k.id);return s.jsxs("div",{className:"flex items-center gap-3 p-3 bg-gray-50 rounded-lg",children:[s.jsx("img",{src:k.avatar,alt:k.name,className:"w-10 h-10 rounded-full object-cover"}),s.jsxs("div",{className:"flex-1",children:[s.jsx("div",{className:"font-medium text-gray-900",children:k.name}),s.jsxs("div",{className:"text-sm text-gray-500",children:[C," 条消息"]})]}),A&&s.jsx("div",{className:"text-xs text-gray-400",children:new Date(A.timestamp).toLocaleTimeString()})]},k.id)})})]}),s.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-6",children:[s.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[s.jsx(kl,{size:24,className:"text-purple-600"}),s.jsx("h3",{className:"font-semibold text-gray-900",children:"讨论统计"})]}),s.jsxs("div",{className:"space-y-3",children:[s.jsxs("div",{className:"flex justify-between items-center",children:[s.jsx("span",{className:"text-gray-600",children:"总消息数"}),s.jsx("span",{className:"font-bold text-lg",children:g.totalMessages})]}),s.jsxs("div",{className:"flex justify-between items-center",children:[s.jsx("span",{className:"text-gray-600",children:"讨论时长"}),s.jsx("span",{className:"font-bold text-lg",children:we(g.activeTime)})]}),s.jsxs("div",{className:"flex justify-between items-center",children:[s.jsx("span",{className:"text-gray-600",children:"参与者数量"}),s.jsx("span",{className:"font-bold text-lg",children:Rt.length})]})]})]}),w.consensus&&s.jsxs("div",{className:"bg-green-50 border border-green-200 rounded-xl p-6",children:[s.jsxs("div",{className:"flex items-center gap-3 mb-3",children:[s.jsx(tt,{size:24,className:"text-green-600"}),s.jsx("h3",{className:"font-semibold text-green-900",children:"讨论结论"})]}),s.jsx("p",{className:"text-green-800",children:w.consensus})]})]})]})})})}function Oa({message:e,agent:t}){const n=()=>{switch(e.type){case"question":return s.jsx(nd,{size:16,className:"text-blue-500"});case"agreement":return s.jsx(ld,{size:16,className:"text-green-500"});case"disagreement":return s.jsx(sd,{size:16,className:"text-red-500"});default:return s.jsx(Be,{size:16,className:"text-gray-500"})}},r=()=>{switch(e.type){case"question":return"border-l-blue-500";case"agreement":return"border-l-green-500";case"disagreement":return"border-l-red-500";default:return"border-l-gray-300"}};return s.jsxs("div",{className:`flex gap-4 p-4 bg-gray-50 rounded-lg border-l-4 ${r()}`,children:[s.jsx("img",{src:t==null?void 0:t.avatar,alt:t==null?void 0:t.name,className:"w-12 h-12 rounded-full object-cover flex-shrink-0"}),s.jsxs("div",{className:"flex-1",children:[s.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[s.jsx("span",{className:"font-semibold text-gray-900",children:t==null?void 0:t.name}),n(),s.jsx("span",{className:"text-xs text-gray-500",children:new Date(e.timestamp).toLocaleTimeString()})]}),s.jsx("p",{className:"text-gray-800 leading-relaxed",children:e.content})]})]})}const Rp=({isOpen:e,onClose:t,onSave:n,editingConfig:r})=>{const[l,i]=D.useState({name:"",provider:"openai",model:"",apiKey:"",baseURL:"",temperature:.7,maxTokens:1e3,systemPrompt:""}),[o,a]=D.useState(!1),[c,f]=D.useState(!1),[x,g]=D.useState(null),[y,v]=D.useState([]),[N,h]=D.useState("");D.useEffect(()=>{r?(i(r),h("")):(i({name:"",provider:"openai",model:"",apiKey:"",baseURL:"",temperature:.7,maxTokens:1e3,systemPrompt:""}),h("")),g(null),v([])},[r,e]);const S=p=>{if(h(p),p){const j=Ra.find(w=>w.id===p);j&&i(w=>({...w,name:j.name,provider:j.provider.toLowerCase(),model:j.model,temperature:j.defaultSettings.temperature,maxTokens:j.defaultSettings.maxTokens}))}},m=(p,j)=>{if(i(w=>({...w,[p]:j})),g(null),y.length>0){const w=U.validateLLMConfig({...l,[p]:j});v(w)}},d=async()=>{const p=U.validateLLMConfig(l);if(p.length>0){v(p);return}f(!0),g(null);try{const j={id:"test",name:l.name,provider:l.provider,model:l.model,apiKey:l.apiKey,baseURL:l.baseURL,temperature:l.temperature,maxTokens:l.maxTokens,systemPrompt:l.systemPrompt},w=await dt.testLLMConfig(j);g({success:w,message:w?"连接测试成功！":"连接测试失败，请检查配置。"})}catch(j){g({success:!1,message:`连接测试失败: ${j instanceof Error?j.message:"未知错误"}`})}finally{f(!1)}},u=()=>{const p=U.validateLLMConfig(l);if(p.length>0){v(p);return}const j={id:(r==null?void 0:r.id)||U.generateLLMConfigId(),name:l.name,provider:l.provider,model:l.model,apiKey:l.apiKey,baseURL:l.baseURL,temperature:l.temperature,maxTokens:l.maxTokens,systemPrompt:l.systemPrompt};n(j),t()};return e?s.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:s.jsxs("div",{className:"bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto",children:[s.jsxs("div",{className:"flex justify-between items-center mb-6",children:[s.jsx("h2",{className:"text-xl font-bold",children:r?"编辑LLM配置":"新建LLM配置"}),s.jsx("button",{onClick:t,className:"text-gray-500 hover:text-gray-700",children:s.jsx(gp,{className:"w-6 h-6"})})]}),!r&&s.jsxs("div",{className:"mb-6",children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"选择预设配置（可选）"}),s.jsxs("select",{value:N,onChange:p=>S(p.target.value),className:"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[s.jsx("option",{value:"",children:"自定义配置"}),Ra.map(p=>s.jsxs("option",{value:p.id,children:[p.name," - ",p.description]},p.id))]})]}),y.length>0&&s.jsxs("div",{className:"mb-4 p-3 bg-red-50 border border-red-200 rounded-md",children:[s.jsxs("div",{className:"flex items-center",children:[s.jsx(An,{className:"w-5 h-5 text-red-500 mr-2"}),s.jsx("span",{className:"text-red-700 font-medium",children:"配置错误"})]}),s.jsx("ul",{className:"mt-2 text-sm text-red-600",children:y.map((p,j)=>s.jsxs("li",{children:["• ",p]},j))})]}),x&&s.jsx("div",{className:`mb-4 p-3 border rounded-md ${x.success?"bg-green-50 border-green-200":"bg-red-50 border-red-200"}`,children:s.jsxs("div",{className:"flex items-center",children:[x.success?s.jsx(tt,{className:"w-5 h-5 text-green-500 mr-2"}):s.jsx(An,{className:"w-5 h-5 text-red-500 mr-2"}),s.jsx("span",{className:`font-medium ${x.success?"text-green-700":"text-red-700"}`,children:x.message})]})}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"配置名称 *"}),s.jsx("input",{type:"text",value:l.name||"",onChange:p=>m("name",p.target.value),className:"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"例如：我的GPT-4配置"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"提供商 *"}),s.jsxs("select",{value:l.provider||"openai",onChange:p=>m("provider",p.target.value),className:"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[s.jsx("option",{value:"openai",children:"OpenAI"}),s.jsx("option",{value:"anthropic",children:"Anthropic"}),s.jsx("option",{value:"azure",children:"Azure OpenAI"}),s.jsx("option",{value:"custom",children:"自定义"})]})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"模型名称 *"}),s.jsx("input",{type:"text",value:l.model||"",onChange:p=>m("model",p.target.value),className:"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"例如：gpt-4, claude-3-opus-20240229"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"API密钥 *"}),s.jsxs("div",{className:"relative",children:[s.jsx("input",{type:o?"text":"password",value:l.apiKey||"",onChange:p=>m("apiKey",p.target.value),className:"w-full p-2 pr-10 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"输入API密钥"}),s.jsx("button",{type:"button",onClick:()=>a(!o),className:"absolute right-2 top-2 text-gray-500 hover:text-gray-700",children:o?s.jsx(np,{className:"w-5 h-5"}):s.jsx(rp,{className:"w-5 h-5"})})]})]}),(l.provider==="azure"||l.provider==="custom")&&s.jsxs("div",{children:[s.jsxs("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:["基础URL ",l.provider==="azure"?"*":"(可选)"]}),s.jsx("input",{type:"text",value:l.baseURL||"",onChange:p=>m("baseURL",p.target.value),className:"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:l.provider==="azure"?"https://your-resource.openai.azure.com":"https://api.example.com"})]}),s.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"温度 (0-2)"}),s.jsx("input",{type:"number",min:"0",max:"2",step:"0.1",value:l.temperature||.7,onChange:p=>m("temperature",parseFloat(p.target.value)),className:"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"最大令牌数"}),s.jsx("input",{type:"number",min:"1",max:"4000",value:l.maxTokens||1e3,onChange:p=>m("maxTokens",parseInt(p.target.value)),className:"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"自定义系统提示词 (可选)"}),s.jsx("textarea",{value:l.systemPrompt||"",onChange:p=>m("systemPrompt",p.target.value),rows:3,className:"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"添加额外的系统提示词..."})]})]}),s.jsxs("div",{className:"flex justify-between mt-6",children:[s.jsxs("button",{onClick:d,disabled:c,className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed",children:[s.jsx(mp,{className:"w-4 h-4 mr-2"}),c?"测试中...":"测试连接"]}),s.jsxs("div",{className:"flex space-x-3",children:[s.jsx("button",{onClick:t,className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50",children:"取消"}),s.jsxs("button",{onClick:u,className:"flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700",children:[s.jsx(cp,{className:"w-4 h-4 mr-2"}),"保存"]})]})]})]})}):null},$p=()=>{const[e,t]=D.useState([]),[n,r]=D.useState(!1),[l,i]=D.useState(null),[o,a]=D.useState(!1),[c,f]=D.useState(!0),[x,g]=D.useState({total:0,byProvider:{},recentlyUsed:[]});D.useEffect(()=>{y()},[]);const y=async()=>{try{f(!0);const[u,p]=await Promise.all([U.getLLMConfigs(),U.getLLMConfigStats()]);t(u),g(p)}catch(u){console.error("Failed to load configs:",u),t([]),g({total:0,byProvider:{},recentlyUsed:[]})}finally{f(!1)}},v=async u=>{try{await U.saveLLMConfig(u),await y()}catch{alert("保存配置失败")}},N=u=>{i(u),r(!0)},h=async u=>{if(confirm("确定要删除这个LLM配置吗？"))try{await U.deleteLLMConfig(u),await y()}catch{alert("删除配置失败")}},S=()=>{i(null),r(!0)},m=async()=>{try{const u=await U.exportLLMConfigs(),p=new Blob([u],{type:"application/json"}),j=URL.createObjectURL(p),w=document.createElement("a");w.href=j,w.download=`llm-configs-${new Date().toISOString().split("T")[0]}.json`,document.body.appendChild(w),w.click(),document.body.removeChild(w),URL.revokeObjectURL(j)}catch{alert("导出失败")}},d=u=>{var w;const p=(w=u.target.files)==null?void 0:w[0];if(!p)return;const j=new FileReader;j.onload=async M=>{var T;try{const O=(T=M.target)==null?void 0:T.result,_=await U.importLLMConfigs(O);_.success>0&&(alert(`成功导入 ${_.success} 个配置`),await y()),_.errors.length>0&&alert(`导入时遇到错误：
${_.errors.join(`
`)}`)}catch{alert("导入失败：文件格式错误")}},j.readAsText(p),u.target.value=""};return c?s.jsx("div",{className:"h-full overflow-y-auto",children:s.jsx("div",{className:"centered-container",children:s.jsx("div",{className:"centered-content",children:s.jsx("div",{className:"flex items-center justify-center h-64",children:s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),s.jsx("p",{className:"text-gray-600",children:"正在加载LLM配置..."})]})})})})}):s.jsx("div",{className:"h-full overflow-y-auto",children:s.jsx("div",{className:"centered-container",children:s.jsxs("div",{className:"centered-content",children:[s.jsxs("div",{className:"flex justify-between items-center mb-6",children:[s.jsxs("div",{children:[s.jsxs("h2",{className:"text-2xl font-bold text-gray-900 flex items-center",children:[s.jsx(Et,{className:"w-8 h-8 mr-3 text-blue-600"}),"LLM配置管理"]}),s.jsx("p",{className:"text-gray-600 mt-1",children:"管理大语言模型配置，为智能体提供AI能力"})]}),s.jsxs("div",{className:"flex space-x-3",children:[s.jsxs("button",{onClick:()=>a(!o),className:"flex items-center px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50",children:[s.jsx(Lt,{className:"w-4 h-4 mr-2"}),"导入/导出"]}),s.jsxs("button",{onClick:S,className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:[s.jsx(wi,{className:"w-4 h-4 mr-2"}),"新建配置"]})]})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6",children:[s.jsxs("div",{className:"bg-white p-4 rounded-lg border border-gray-200",children:[s.jsx("div",{className:"text-2xl font-bold text-blue-600",children:x.total}),s.jsx("div",{className:"text-sm text-gray-600",children:"总配置数"})]}),s.jsxs("div",{className:"bg-white p-4 rounded-lg border border-gray-200",children:[s.jsx("div",{className:"text-2xl font-bold text-green-600",children:Object.keys(x.byProvider).length}),s.jsx("div",{className:"text-sm text-gray-600",children:"支持的提供商"})]}),s.jsxs("div",{className:"bg-white p-4 rounded-lg border border-gray-200",children:[s.jsx("div",{className:"text-2xl font-bold text-purple-600",children:x.recentlyUsed.length}),s.jsx("div",{className:"text-sm text-gray-600",children:"最近使用"})]})]}),o&&s.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg mb-6 border border-gray-200",children:[s.jsx("h3",{className:"text-lg font-medium mb-3",children:"导入/导出配置"}),s.jsxs("div",{className:"flex space-x-4",children:[s.jsxs("button",{onClick:m,className:"flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700",children:[s.jsx(ji,{className:"w-4 h-4 mr-2"}),"导出配置"]}),s.jsxs("label",{className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 cursor-pointer",children:[s.jsx(Ni,{className:"w-4 h-4 mr-2"}),"导入配置",s.jsx("input",{type:"file",accept:".json",onChange:d,className:"hidden"})]})]}),s.jsx("p",{className:"text-sm text-gray-600 mt-2",children:"导出的配置文件会隐藏API密钥，导入时需要重新设置"})]}),s.jsx("div",{className:"bg-white rounded-lg border border-gray-200",children:e.length===0?s.jsxs("div",{className:"p-8 text-center",children:[s.jsx(Et,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),s.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"还没有LLM配置"}),s.jsx("p",{className:"text-gray-600 mb-4",children:"创建第一个LLM配置来为智能体提供AI能力"}),s.jsxs("button",{onClick:S,className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 mx-auto",children:[s.jsx(wi,{className:"w-4 h-4 mr-2"}),"新建配置"]})]}):s.jsx("div",{className:"divide-y divide-gray-200",children:e.map(u=>s.jsxs("div",{className:"p-4 hover:bg-gray-50",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsx("div",{className:"text-2xl",children:Is(u.provider)}),s.jsxs("div",{children:[s.jsx("h3",{className:"text-lg font-medium text-gray-900",children:u.name}),s.jsxs("div",{className:"flex items-center space-x-2 mt-1",children:[s.jsx("span",{className:`px-2 py-1 text-xs rounded-full ${Ps(u.provider)}`,children:u.provider.toUpperCase()}),s.jsx("span",{className:"text-sm text-gray-600",children:u.model})]})]})]}),s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsxs("div",{className:"text-right text-sm text-gray-600",children:[s.jsxs("div",{children:["温度: ",u.temperature]}),s.jsxs("div",{children:["令牌: ",u.maxTokens]})]}),s.jsxs("div",{className:"flex space-x-1",children:[s.jsx("button",{onClick:()=>N(u),className:"p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-md",title:"编辑配置",children:s.jsx(rd,{className:"w-4 h-4"})}),s.jsx("button",{onClick:()=>h(u.id),className:"p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-md",title:"删除配置",children:s.jsx(jr,{className:"w-4 h-4"})})]})]})]}),u.systemPrompt&&s.jsxs("div",{className:"mt-3 p-3 bg-gray-50 rounded-md",children:[s.jsx("div",{className:"text-sm text-gray-600",children:s.jsx("strong",{children:"自定义系统提示词:"})}),s.jsx("div",{className:"text-sm text-gray-800 mt-1 line-clamp-2",children:u.systemPrompt})]})]},u.id))})}),Object.keys(x.byProvider).length>0&&s.jsxs("div",{className:"mt-6 bg-white p-4 rounded-lg border border-gray-200",children:[s.jsx("h3",{className:"text-lg font-medium mb-3",children:"提供商分布"}),s.jsx("div",{className:"flex flex-wrap gap-2",children:Object.entries(x.byProvider).map(([u,p])=>s.jsxs("div",{className:`flex items-center px-3 py-1 rounded-full text-sm ${Ps(u)}`,children:[s.jsx("span",{className:"mr-1",children:Is(u)}),u.toUpperCase(),": ",p]},u))})]}),s.jsx(Rp,{isOpen:n,onClose:()=>{r(!1),i(null)},onSave:v,editingConfig:l})]})})})},Op=()=>{const{state:e,exportData:t,importData:n,clearAllData:r}=Pt(),[l,i]=D.useState(!1),[o,a]=D.useState(!1),[c,f]=D.useState(null),[x,g]=D.useState(!1),[y,v]=D.useState(null);D.useEffect(()=>{(async()=>{try{const u=await U.getStorageInfo();v(u)}catch(u){console.error("Failed to fetch storage info:",u),v({used:0,available:0,total:0})}})()},[]);const N=d=>{if(d===0)return"0 Bytes";const u=1024,p=["Bytes","KB","MB","GB"],j=Math.floor(Math.log(d)/Math.log(u));return parseFloat((d/Math.pow(u,j)).toFixed(2))+" "+p[j]},h=async()=>{i(!0);try{const d=await t(),u=new Blob([d],{type:"application/json"}),p=URL.createObjectURL(u),j=document.createElement("a");j.href=p,j.download=`multi-agent-system-backup-${new Date().toISOString().split("T")[0]}.json`,document.body.appendChild(j),j.click(),document.body.removeChild(j),URL.revokeObjectURL(p)}catch(d){f({success:!1,message:"导出失败: "+(d instanceof Error?d.message:"未知错误")})}finally{i(!1)}},S=async d=>{var p;const u=(p=d.target.files)==null?void 0:p[0];if(u){a(!0),f(null);try{const j=await u.text(),w=await n(j);f({success:w,message:w?"数据导入成功！":"数据导入失败，请检查文件格式。"})}catch(j){f({success:!1,message:"导入失败: "+(j instanceof Error?j.message:"未知错误")})}finally{a(!1),d.target.value=""}}},m=async()=>{try{await r(),g(!1),f({success:!0,message:"所有数据已清除，系统已重置为默认状态。"})}catch(d){f({success:!1,message:"清除数据失败: "+(d instanceof Error?d.message:"未知错误")})}};return s.jsx("div",{className:"h-full overflow-y-auto",children:s.jsx("div",{className:"centered-container",children:s.jsxs("div",{className:"centered-content",children:[s.jsx("div",{className:"flex justify-between items-center mb-6",children:s.jsxs("div",{children:[s.jsxs("h2",{className:"text-2xl font-bold text-gray-900 flex items-center",children:[s.jsx(vo,{className:"w-8 h-8 mr-3 text-blue-600"}),"数据管理"]}),s.jsx("p",{className:"text-gray-600 mt-1",children:"管理系统数据的备份、恢复和清理"})]})}),c&&s.jsx("div",{className:`mb-6 p-4 rounded-lg border ${c.success?"bg-green-50 border-green-200":"bg-red-50 border-red-200"}`,children:s.jsxs("div",{className:"flex items-center",children:[c.success?s.jsx(tt,{className:"w-5 h-5 text-green-500 mr-2"}):s.jsx(Ms,{className:"w-5 h-5 text-red-500 mr-2"}),s.jsx("span",{className:`font-medium ${c.success?"text-green-700":"text-red-700"}`,children:c.message})]})}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-8",children:[s.jsx("div",{className:"bg-white p-4 rounded-lg border border-gray-200",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("div",{className:"text-2xl font-bold text-blue-600",children:e.agents.length}),s.jsx("div",{className:"text-sm text-gray-600",children:"智能体"})]}),s.jsx(Lt,{className:"w-8 h-8 text-blue-500"})]})}),s.jsx("div",{className:"bg-white p-4 rounded-lg border border-gray-200",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("div",{className:"text-2xl font-bold text-green-600",children:e.allDiscussions.length}),s.jsx("div",{className:"text-sm text-gray-600",children:"历史讨论"})]}),s.jsx(sp,{className:"w-8 h-8 text-green-500"})]})}),s.jsx("div",{className:"bg-white p-4 rounded-lg border border-gray-200",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("div",{className:"text-2xl font-bold text-purple-600",children:y?N(y.used):"加载中..."}),s.jsx("div",{className:"text-sm text-gray-600",children:"已用存储"})]}),s.jsx(lp,{className:"w-8 h-8 text-purple-500"})]})})]}),s.jsxs("div",{className:"bg-white p-6 rounded-lg border border-gray-200 mb-8",children:[s.jsx("h3",{className:"text-lg font-medium mb-4",children:"存储使用情况"}),y?s.jsxs("div",{className:"space-y-3",children:[s.jsxs("div",{className:"flex justify-between text-sm",children:[s.jsx("span",{children:"已使用"}),s.jsx("span",{children:N(y.used)})]}),s.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:s.jsx("div",{className:"bg-blue-500 h-2 rounded-full transition-all",style:{width:`${y.used/y.total*100}%`}})}),s.jsxs("div",{className:"flex justify-between text-sm text-gray-600",children:[s.jsxs("span",{children:["可用: ",N(y.available)]}),s.jsxs("span",{children:["总计: ",N(y.total)]})]})]}):s.jsx("div",{className:"text-center text-gray-500",children:"加载存储信息中..."})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[s.jsxs("div",{className:"bg-white p-6 rounded-lg border border-gray-200",children:[s.jsxs("div",{className:"flex items-center mb-4",children:[s.jsx(ji,{className:"w-6 h-6 text-green-600 mr-3"}),s.jsx("h3",{className:"text-lg font-medium",children:"导出数据"})]}),s.jsx("p",{className:"text-gray-600 mb-4 text-sm",children:"将所有配置和历史记录导出为JSON文件，用于备份或迁移。"}),s.jsxs("button",{onClick:h,disabled:l,className:"w-full flex items-center justify-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed",children:[s.jsx(ji,{className:"w-4 h-4 mr-2"}),l?"导出中...":"导出数据"]})]}),s.jsxs("div",{className:"bg-white p-6 rounded-lg border border-gray-200",children:[s.jsxs("div",{className:"flex items-center mb-4",children:[s.jsx(Ni,{className:"w-6 h-6 text-blue-600 mr-3"}),s.jsx("h3",{className:"text-lg font-medium",children:"导入数据"})]}),s.jsx("p",{className:"text-gray-600 mb-4 text-sm",children:"从备份文件恢复配置和历史记录。将覆盖当前数据。"}),s.jsxs("label",{className:"w-full flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 cursor-pointer",children:[s.jsx(Ni,{className:"w-4 h-4 mr-2"}),o?"导入中...":"选择文件",s.jsx("input",{type:"file",accept:".json",onChange:S,disabled:o,className:"hidden"})]})]}),s.jsxs("div",{className:"bg-white p-6 rounded-lg border border-gray-200",children:[s.jsxs("div",{className:"flex items-center mb-4",children:[s.jsx(jr,{className:"w-6 h-6 text-red-600 mr-3"}),s.jsx("h3",{className:"text-lg font-medium",children:"清除数据"})]}),s.jsx("p",{className:"text-gray-600 mb-4 text-sm",children:"清除所有数据并重置为默认状态。此操作不可撤销。"}),x?s.jsxs("div",{className:"space-y-2",children:[s.jsxs("div",{className:"flex items-center p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700",children:[s.jsx(Ms,{className:"w-4 h-4 mr-2"}),"确定要清除所有数据吗？"]}),s.jsxs("div",{className:"flex space-x-2",children:[s.jsx("button",{onClick:m,className:"flex-1 px-3 py-2 bg-red-600 text-white rounded text-sm hover:bg-red-700",children:"确认清除"}),s.jsx("button",{onClick:()=>g(!1),className:"flex-1 px-3 py-2 bg-gray-300 text-gray-700 rounded text-sm hover:bg-gray-400",children:"取消"})]})]}):s.jsxs("button",{onClick:()=>g(!0),className:"w-full flex items-center justify-center px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700",children:[s.jsx(jr,{className:"w-4 h-4 mr-2"}),"清除所有数据"]})]})]}),s.jsx("div",{className:"mt-8 bg-blue-50 border border-blue-200 rounded-lg p-4",children:s.jsxs("div",{className:"flex items-start",children:[s.jsx(ip,{className:"w-5 h-5 text-blue-500 mr-2 mt-0.5"}),s.jsxs("div",{className:"text-sm text-blue-700",children:[s.jsx("div",{className:"font-medium mb-1",children:"使用说明："}),s.jsxs("ul",{className:"space-y-1 text-xs",children:[s.jsx("li",{children:"• 导出的数据包含智能体配置、LLM配置、讨论历史等所有信息"}),s.jsx("li",{children:"• 导入数据会覆盖当前所有配置，建议先导出备份"}),s.jsx("li",{children:"• 清除数据会删除所有自定义配置，但会保留默认智能体"}),s.jsx("li",{children:"• 数据存储在浏览器本地，清除浏览器数据会丢失所有配置"})]})]})]})})]})})})},Up=({onNavigate:e})=>{const{state:t,dispatch:n,setCurrentDiscussion:r}=Pt(),[l,i]=D.useState(""),[o,a]=D.useState("date"),[c,f]=D.useState("all"),[x,g]=D.useState(null),[y,v]=D.useState(null),N=async u=>{if(confirm("确定要删除这条讨论记录吗？此操作不可撤销。"))try{v(u),await U.deleteDiscussion(u);const p=await U.getDiscussions();n({type:"SET_ALL_DISCUSSIONS",payload:p})}catch(p){console.error("删除讨论失败:",p),alert("删除失败，请重试")}finally{v(null)}},h=u=>{r(u),e("discussion")},S=t.allDiscussions.filter(u=>{const p=u.topic.toLowerCase().includes(l.toLowerCase()),j=c==="all"||u.status===c;return p&&j}).sort((u,p)=>{switch(o){case"date":return new Date(p.createdAt).getTime()-new Date(u.createdAt).getTime();case"topic":return u.topic.localeCompare(p.topic);case"messages":return p.messages.length-u.messages.length;default:return 0}}),m=u=>{switch(u){case"consensus":return s.jsx(tt,{className:"w-4 h-4 text-green-500"});case"ended":return s.jsx(Ts,{className:"w-4 h-4 text-gray-500"});default:return s.jsx(Be,{className:"w-4 h-4 text-blue-500"})}},d=u=>{switch(u){case"consensus":return"已达成共识";case"ended":return"已结束";default:return"进行中"}};return s.jsx("div",{className:"h-[calc(100vh-4rem)] bg-gradient-to-br from-gray-50 to-blue-50 w-full overflow-hidden",children:s.jsx("div",{className:"h-full p-6",children:s.jsxs("div",{className:"bg-white rounded-xl shadow-lg h-full flex flex-col",children:[s.jsxs("div",{className:"bg-gradient-to-r from-purple-600 to-blue-600 text-white p-6 rounded-t-xl",children:[s.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[s.jsx(As,{size:32}),s.jsxs("div",{children:[s.jsx("h1",{className:"text-2xl font-bold",children:"讨论历史"}),s.jsx("p",{className:"text-purple-100",children:"查看和管理历史讨论记录"})]})]}),s.jsxs("div",{className:"flex gap-4 items-center",children:[s.jsxs("div",{className:"flex-1 relative",children:[s.jsx(up,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),s.jsx("input",{type:"text",placeholder:"搜索讨论话题...",value:l,onChange:u=>i(u.target.value),className:"w-full pl-10 pr-4 py-2 rounded-lg text-gray-900 placeholder-gray-500"})]}),s.jsxs("select",{value:o,onChange:u=>a(u.target.value),className:"px-4 py-2 rounded-lg text-gray-900",children:[s.jsx("option",{value:"date",children:"按时间排序"}),s.jsx("option",{value:"topic",children:"按话题排序"}),s.jsx("option",{value:"messages",children:"按消息数排序"})]}),s.jsxs("select",{value:c,onChange:u=>f(u.target.value),className:"px-4 py-2 rounded-lg text-gray-900",children:[s.jsx("option",{value:"all",children:"全部状态"}),s.jsx("option",{value:"consensus",children:"已达成共识"}),s.jsx("option",{value:"ended",children:"已结束"})]})]})]}),s.jsx("div",{className:"flex-1 overflow-y-auto p-6",children:S.length===0?s.jsxs("div",{className:"text-center py-12",children:[s.jsx(As,{size:64,className:"text-gray-400 mx-auto mb-4"}),s.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"暂无讨论记录"}),s.jsx("p",{className:"text-gray-600",children:"开始一个新的讨论来创建历史记录"})]}):s.jsx("div",{className:"space-y-4",children:S.map(u=>{const p=t.agents.filter(j=>u.participants.includes(j.id));return s.jsx("div",{className:"bg-gray-50 rounded-lg border border-gray-200",children:s.jsxs("div",{className:"p-4",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex-1",children:[s.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[m(u.status),s.jsx("h3",{className:"font-semibold text-gray-900",children:u.topic}),s.jsx("span",{className:"text-sm text-gray-500",children:d(u.status)})]}),s.jsxs("div",{className:"flex items-center gap-6 text-sm text-gray-600",children:[s.jsxs("div",{className:"flex items-center gap-1",children:[s.jsx(Zf,{className:"w-4 h-4"}),new Date(u.createdAt).toLocaleString()]}),s.jsxs("div",{className:"flex items-center gap-1",children:[s.jsx(Be,{className:"w-4 h-4"}),u.messages.length," 条消息"]}),s.jsxs("div",{className:"flex items-center gap-1",children:[s.jsx(st,{className:"w-4 h-4"}),p.length," 位参与者"]}),u.consensusScore!==void 0&&s.jsxs("div",{className:"flex items-center gap-1",children:[s.jsx(id,{className:"w-4 h-4"}),"共识度 ",Math.round(u.consensusScore),"%"]})]})]}),s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx("button",{onClick:()=>h(u),className:"p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-colors",title:"转到聊天室",children:s.jsx(tp,{className:"w-4 h-4"})}),s.jsx("button",{onClick:()=>N(u.id),disabled:y===u.id,className:"p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-md disabled:opacity-50 disabled:cursor-not-allowed transition-colors",title:"删除记录",children:y===u.id?s.jsx("div",{className:"animate-spin w-4 h-4 border-2 border-red-500 border-t-transparent rounded-full"}):s.jsx(jr,{className:"w-4 h-4"})}),s.jsx("button",{onClick:()=>g(x===u.id?null:u.id),className:"p-2 text-gray-600 hover:text-purple-600 hover:bg-purple-50 rounded-md transition-colors",title:"查看详情",children:x===u.id?s.jsx(ep,{className:"w-4 h-4"}):s.jsx(Jf,{className:"w-4 h-4"})})]})]}),x===u.id&&s.jsxs("div",{className:"mt-4 pt-4 border-t border-gray-200",children:[s.jsxs("div",{className:"mb-4",children:[s.jsx("h4",{className:"font-medium text-gray-900 mb-2",children:"参与者"}),s.jsx("div",{className:"flex flex-wrap gap-2",children:p.map(j=>s.jsxs("div",{className:"flex items-center gap-2 bg-white px-3 py-1 rounded-full border",children:[s.jsx("img",{src:j.avatar,alt:j.name,className:"w-6 h-6 rounded-full object-cover"}),s.jsx("span",{className:"text-sm font-medium",children:j.name})]},j.id))})]}),u.consensus&&s.jsxs("div",{className:"mb-4",children:[s.jsx("h4",{className:"font-medium text-gray-900 mb-2",children:"讨论结论"}),s.jsx("div",{className:"bg-green-50 border border-green-200 rounded-lg p-3",children:s.jsx("p",{className:"text-green-800",children:u.consensus})})]}),s.jsxs("div",{children:[s.jsx("h4",{className:"font-medium text-gray-900 mb-2",children:"消息预览"}),s.jsxs("div",{className:"space-y-2 max-h-60 overflow-y-auto",children:[u.messages.slice(0,5).map(j=>{const w=p.find(M=>M.id===j.agentId);return s.jsxs("div",{className:"bg-white p-3 rounded border",children:[s.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[s.jsx("span",{className:"font-medium text-sm",children:w==null?void 0:w.name}),s.jsx("span",{className:"text-xs text-gray-500",children:new Date(j.timestamp).toLocaleTimeString()})]}),s.jsx("p",{className:"text-sm text-gray-700 line-clamp-2",children:j.content})]},j.id)}),u.messages.length>5&&s.jsxs("div",{className:"text-center text-sm text-gray-500",children:["还有 ",u.messages.length-5," 条消息..."]})]})]})]})]})},u.id)})})})]})})})},Fp=()=>{const{state:e}=Pt(),[t,n]=D.useState(!1),[r,l]=D.useState([{id:"storage",label:"初始化存储服务",status:"pending"},{id:"server",label:"检查服务器连接",status:"pending"},{id:"agents",label:"加载智能体配置",status:"pending"},{id:"llm",label:"加载LLM配置",status:"pending"},{id:"discussions",label:"加载讨论历史",status:"pending"}]);D.useEffect(()=>{l(c=>c.map(f=>{const x=c.findIndex(y=>y.id===f.id),g=c.findIndex(y=>y.id===e.loadingStep);return x<g?{...f,status:"completed"}:x===g?{...f,status:"loading"}:{...f,status:"pending"}}))},[e.loadingStep]),D.useEffect(()=>{const c=setTimeout(()=>{n(!0)},1e4);return()=>clearTimeout(c)},[]);const i=c=>{switch(c){case"completed":return s.jsx(tt,{className:"w-4 h-4 text-green-500"});case"loading":return s.jsx(Ia,{className:"w-4 h-4 text-blue-500 animate-spin"});case"error":return s.jsx(An,{className:"w-4 h-4 text-red-500"});default:return s.jsx(Ts,{className:"w-4 h-4 text-gray-400"})}},o=r.filter(c=>c.status==="completed").length,a=o/r.length*100;return s.jsx("div",{className:"min-h-screen bg-gradient-to-br from-indigo-50 via-blue-50 to-purple-50 flex items-center justify-center",children:s.jsxs("div",{className:"text-center max-w-md mx-auto",children:[s.jsxs("div",{className:"flex items-center justify-center gap-3 mb-8",children:[s.jsx(Et,{size:48,className:"text-blue-600 animate-pulse"}),s.jsx("h1",{className:"text-4xl font-bold text-gray-900",children:"多智能体讨论系统"})]}),s.jsxs("div",{className:"flex items-center justify-center gap-3 mb-6",children:[s.jsx(Ia,{className:"w-6 h-6 text-blue-600 animate-spin"}),s.jsx("span",{className:"text-lg text-gray-600",children:"正在初始化系统..."})]}),s.jsxs("div",{className:"w-80 mx-auto mb-6",children:[s.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:s.jsx("div",{className:"bg-blue-500 h-2 rounded-full transition-all duration-500",style:{width:`${a}%`}})}),s.jsxs("div",{className:"mt-2 text-sm text-gray-500",children:[o,"/",r.length," 步骤完成"]})]}),s.jsx("div",{className:"space-y-3 text-sm",children:r.map(c=>s.jsxs("div",{className:"flex items-center justify-center gap-3",children:[i(c.status),s.jsx("span",{className:`${c.status==="completed"?"text-green-600":c.status==="loading"?"text-blue-600":c.status==="error"?"text-red-600":"text-gray-500"}`,children:c.label})]},c.id))}),t&&s.jsxs("div",{className:"mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg",children:[s.jsxs("div",{className:"flex items-center gap-2 text-yellow-800",children:[s.jsx(An,{className:"w-5 h-5"}),s.jsx("span",{className:"font-medium",children:"加载时间较长"})]}),s.jsx("p",{className:"text-sm text-yellow-700 mt-1",children:"连接服务器超时，原因是网络较慢或后台服务不可用。"})]})]})})};class Vp extends D.Component{constructor(n){super(n);ge(this,"handleReload",()=>{window.location.reload()});ge(this,"handleReset",()=>{this.setState({hasError:!1,error:null,errorInfo:null})});this.state={hasError:!1,error:null,errorInfo:null}}static getDerivedStateFromError(n){return{hasError:!0,error:n,errorInfo:null}}componentDidCatch(n,r){console.error("ErrorBoundary caught an error:",n,r),this.setState({error:n,errorInfo:r})}render(){var n,r,l;if(this.state.hasError){const i=(r=(n=this.state.error)==null?void 0:n.message)==null?void 0:r.includes("无法连接到后端服务器");return s.jsx("div",{className:"min-h-screen bg-gradient-to-br from-red-50 via-orange-50 to-yellow-50 flex items-center justify-center",children:s.jsxs("div",{className:"text-center max-w-2xl mx-auto p-8",children:[s.jsxs("div",{className:"flex items-center justify-center gap-3 mb-6",children:[s.jsx(Ms,{size:48,className:"text-red-500"}),s.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:i?"无法连接后端服务":"系统初始化失败"})]}),s.jsxs("div",{className:"bg-white rounded-lg shadow-lg p-6 mb-6",children:[s.jsx("h2",{className:"text-lg font-semibold text-gray-800 mb-4",children:"错误详情"}),s.jsx("div",{className:"text-left bg-gray-50 rounded p-4 mb-4",children:s.jsx("p",{className:"text-red-600 font-mono text-sm",children:((l=this.state.error)==null?void 0:l.message)||"未知错误"})}),s.jsx("div",{className:"text-sm text-gray-600 mb-4",children:i?s.jsxs(s.Fragment,{children:[s.jsx("p",{className:"font-medium mb-2",children:"请检查以下项目："}),s.jsxs("ul",{className:"list-disc list-inside space-y-1",children:[s.jsx("li",{children:"后端服务是否已启动（端口5000）"}),s.jsx("li",{children:"网络连接是否正常"}),s.jsx("li",{children:"防火墙是否阻止了连接"}),s.jsx("li",{children:"后端服务地址配置是否正确"})]})]}):s.jsxs(s.Fragment,{children:[s.jsx("p",{children:"可能的原因："}),s.jsxs("ul",{className:"list-disc list-inside mt-2 space-y-1",children:[s.jsx("li",{children:"网络连接问题"}),s.jsx("li",{children:"后端服务器不可用"}),s.jsx("li",{children:"浏览器存储空间不足"}),s.jsx("li",{children:"配置文件损坏"})]})]})})]}),s.jsxs("div",{className:"flex gap-4 justify-center",children:[s.jsxs("button",{onClick:this.handleReload,className:"flex items-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[s.jsx(ap,{size:20}),"重新加载页面"]}),s.jsx("button",{onClick:this.handleReset,className:"flex items-center gap-2 px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors",children:"重试初始化"})]}),i&&s.jsxs("div",{className:"mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg",children:[s.jsx("p",{className:"text-blue-800 font-medium mb-2",children:"启动后端服务："}),s.jsxs("div",{className:"text-sm text-blue-700 space-y-1",children:[s.jsxs("p",{children:["Windows: 运行 ",s.jsx("code",{className:"bg-blue-100 px-1 rounded",children:"start.bat"})]}),s.jsxs("p",{children:["Linux/macOS: 运行 ",s.jsx("code",{className:"bg-blue-100 px-1 rounded",children:"./start.sh"})]})]})]})]})})}return this.props.children}}function Hp(){const{state:e}=Pt(),[t,n]=D.useState("home");if(Ya.useEffect(()=>{e.isDiscussionActive&&e.currentDiscussion&&n("discussion")},[e.isDiscussionActive,e.currentDiscussion]),e.isLoading)return s.jsx(Fp,{});const r=()=>{switch(t){case"agents":return s.jsx(wp,{});case"llm":return s.jsx($p,{});case"data":return s.jsx(Op,{});case"history":return s.jsx(Up,{onNavigate:n});case"setup":return s.jsx(kp,{});case"discussion":return s.jsx(zp,{});default:return s.jsx(Bp,{onNavigate:n})}};return s.jsxs("div",{className:"h-screen bg-gray-50 w-full flex flex-col",children:[t!=="home"&&s.jsx("nav",{className:"flex-shrink-0 bg-white shadow-sm border-b border-gray-200 w-full",children:s.jsx("div",{className:"w-full px-6",children:s.jsxs("div",{className:"flex items-center justify-between h-16",children:[s.jsxs("div",{className:"flex items-center gap-8",children:[s.jsxs("button",{onClick:()=>n("home"),className:"flex items-center gap-2 text-gray-900 hover:text-blue-600 font-medium",children:[s.jsx(Et,{size:24,className:"text-blue-600"}),"多智能体讨论系统"]}),s.jsxs("div",{className:"flex gap-6",children:[s.jsxs("button",{onClick:()=>n("agents"),className:`flex items-center gap-2 px-3 py-2 rounded-md transition-colors ${t==="agents"?"bg-blue-100 text-blue-700":"text-gray-600 hover:text-gray-900"}`,children:[s.jsx(st,{size:20}),"智能体管理"]}),s.jsxs("button",{onClick:()=>n("llm"),className:`flex items-center gap-2 px-3 py-2 rounded-md transition-colors ${t==="llm"?"bg-orange-100 text-orange-700":"text-gray-600 hover:text-gray-900"}`,children:[s.jsx(td,{size:20}),"LLM管理"]}),s.jsxs("button",{onClick:()=>n("history"),className:`flex items-center gap-2 px-3 py-2 rounded-md transition-colors ${t==="history"?"bg-purple-100 text-purple-700":"text-gray-600 hover:text-gray-900"}`,children:[s.jsx(As,{size:20}),"讨论历史"]}),s.jsxs("button",{onClick:()=>n("data"),className:`flex items-center gap-2 px-3 py-2 rounded-md transition-colors ${t==="data"?"bg-indigo-100 text-indigo-700":"text-gray-600 hover:text-gray-900"}`,children:[s.jsx(vo,{size:20}),"数据管理"]}),s.jsxs("button",{onClick:()=>n("setup"),className:`flex items-center gap-2 px-3 py-2 rounded-md transition-colors ${t==="setup"?"bg-blue-100 text-blue-700":"text-gray-600 hover:text-gray-900"}`,disabled:e.agents.length===0,children:[s.jsx(Lt,{size:20}),"创建讨论"]}),e.isDiscussionActive&&s.jsxs("button",{onClick:()=>n("discussion"),className:`flex items-center gap-2 px-3 py-2 rounded-md transition-colors ${t==="discussion"?"bg-green-100 text-green-700":"text-green-600 hover:text-green-700"}`,children:[s.jsx(Be,{size:20}),"讨论进行中",s.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full animate-pulse"})]})]})]}),s.jsxs("div",{className:"flex items-center gap-4",children:[s.jsxs("span",{className:"text-sm text-gray-500",children:[e.agents.length," 个智能体"]}),e.isDiscussionActive&&s.jsxs("div",{className:"flex items-center gap-2 bg-green-50 text-green-700 px-3 py-1 rounded-full text-sm",children:[s.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),"讨论中"]})]})]})})}),s.jsx("div",{className:"flex-1 overflow-hidden",children:r()})]})}function Bp({onNavigate:e}){var n;const{state:t}=Pt();return s.jsx("div",{className:"h-full bg-gradient-to-br from-indigo-50 via-blue-50 to-purple-50 w-full fixed-layout overflow-y-auto",children:s.jsxs("div",{className:"w-full px-6 py-12 fixed-layout",children:[s.jsxs("div",{className:"text-center mb-16",children:[s.jsxs("div",{className:"flex items-center justify-center gap-3 mb-6",children:[s.jsx(Et,{size:48,className:"text-blue-600"}),s.jsx("h1",{className:"text-5xl font-bold text-gray-900",children:"多智能体讨论系统"})]}),s.jsx("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed",children:"通过配置不同专业背景和思维方式的AI智能体，创建富有洞察力的讨论环境， 探索复杂问题的多维度解决方案，并达成有价值的共识。"}),s.jsxs("div",{className:"flex items-center justify-center gap-8 mt-8 text-sm text-gray-500",children:[s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(za,{size:16,className:"text-yellow-500"}),"支持2-8个智能体同时讨论"]}),s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(Et,{size:16,className:"text-purple-500"}),"智能共识判断算法"]}),s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(Be,{size:16,className:"text-blue-500"}),"实时讨论模拟"]})]})]}),s.jsxs("div",{className:"space-y-8 mb-16",children:[s.jsxs("div",{className:"flex flex-wrap justify-center gap-6",children:[s.jsx("div",{className:"group",children:s.jsxs("div",{className:"bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all p-8 border border-gray-100 group-hover:border-blue-200 fixed-size-card",children:[s.jsxs("div",{className:"flex items-center gap-4 mb-6",children:[s.jsx("div",{className:"w-16 h-16 bg-blue-100 rounded-xl flex items-center justify-center group-hover:bg-blue-200 transition-colors",children:s.jsx(st,{size:32,className:"text-blue-600"})}),s.jsxs("div",{children:[s.jsx("h3",{className:"text-xl font-bold text-gray-900",children:"智能体管理"}),s.jsx("p",{className:"text-gray-500",children:"配置AI智能体"})]})]}),s.jsx("p",{className:"text-gray-600 mb-6 leading-relaxed",children:"创建和配置具有不同专业背景、思维方式和性格特征的智能体。 每个智能体都有独特的知识领域和讨论风格。"}),s.jsxs("div",{className:"mb-6",children:[s.jsxs("div",{className:"flex items-center justify-between text-sm text-gray-500 mb-2",children:[s.jsx("span",{children:"当前智能体数量"}),s.jsxs("span",{className:"font-bold text-blue-600 text-lg",children:[t.agents.length,"/8"]})]}),s.jsx("div",{className:"w-full h-2 bg-gray-200 rounded-full",children:s.jsx("div",{className:"h-full bg-blue-500 rounded-full transition-all",style:{width:`${t.agents.length/8*100}%`}})})]}),s.jsx("button",{onClick:()=>e("agents"),className:"w-full bg-blue-600 text-white py-3 rounded-xl hover:bg-blue-700 transition-colors font-medium",children:"管理智能体"})]})}),s.jsx("div",{className:"group",children:s.jsxs("div",{className:"bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all p-8 border border-gray-100 group-hover:border-orange-200 fixed-size-card",children:[s.jsxs("div",{className:"flex items-center gap-4 mb-6",children:[s.jsx("div",{className:"w-16 h-16 bg-orange-100 rounded-xl flex items-center justify-center group-hover:bg-orange-200 transition-colors",children:s.jsx(td,{size:32,className:"text-orange-600"})}),s.jsxs("div",{children:[s.jsx("h3",{className:"text-xl font-bold text-gray-900",children:"LLM管理"}),s.jsx("p",{className:"text-gray-500",children:"配置大语言模型"})]})]}),s.jsx("p",{className:"text-gray-600 mb-6 leading-relaxed",children:"配置和管理大语言模型，为智能体提供真实的AI对话能力。 支持OpenAI、Anthropic等多种提供商。"}),s.jsxs("div",{className:"space-y-3 mb-6",children:[s.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[s.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),"支持多种LLM提供商"]}),s.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[s.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),"个性化配置参数"]}),s.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[s.jsx("div",{className:"w-2 h-2 bg-purple-500 rounded-full"}),"连接测试功能"]})]}),s.jsx("button",{onClick:()=>e("llm"),className:"w-full bg-orange-600 text-white py-3 rounded-xl hover:bg-orange-700 transition-colors font-medium",children:"管理LLM配置"})]})}),s.jsx("div",{className:"group",children:s.jsxs("div",{className:"bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all p-8 border border-gray-100 group-hover:border-indigo-200 fixed-size-card",children:[s.jsxs("div",{className:"flex items-center gap-4 mb-6",children:[s.jsx("div",{className:"w-16 h-16 bg-indigo-100 rounded-xl flex items-center justify-center group-hover:bg-indigo-200 transition-colors",children:s.jsx(vo,{size:32,className:"text-indigo-600"})}),s.jsxs("div",{children:[s.jsx("h3",{className:"text-xl font-bold text-gray-900",children:"数据管理"}),s.jsx("p",{className:"text-gray-500",children:"备份与恢复"})]})]}),s.jsx("p",{className:"text-gray-600 mb-6 leading-relaxed",children:"管理系统数据的备份、恢复和清理，确保配置和历史记录的安全。"}),s.jsxs("div",{className:"space-y-3 mb-6",children:[s.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[s.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),"数据导出备份"]}),s.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[s.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),"配置导入恢复"]}),s.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[s.jsx("div",{className:"w-2 h-2 bg-red-500 rounded-full"}),"数据清理重置"]})]}),s.jsx("button",{onClick:()=>e("data"),className:"w-full bg-indigo-600 text-white py-3 rounded-xl hover:bg-indigo-700 transition-colors font-medium",children:"管理数据"})]})})]}),s.jsxs("div",{className:"flex flex-wrap justify-center gap-6",children:[s.jsx("div",{className:"group",children:s.jsxs("div",{className:"bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all p-8 border border-gray-100 group-hover:border-purple-200 fixed-size-card",children:[s.jsxs("div",{className:"flex items-center gap-4 mb-6",children:[s.jsx("div",{className:"w-16 h-16 bg-purple-100 rounded-xl flex items-center justify-center group-hover:bg-purple-200 transition-colors",children:s.jsx(Lt,{size:32,className:"text-purple-600"})}),s.jsxs("div",{children:[s.jsx("h3",{className:"text-xl font-bold text-gray-900",children:"创建讨论"}),s.jsx("p",{className:"text-gray-500",children:"配置讨论参数"})]})]}),s.jsx("p",{className:"text-gray-600 mb-6 leading-relaxed",children:"设置讨论话题、选择参与的智能体、配置讨论模式， 开始一场富有见解的AI讨论。"}),s.jsxs("div",{className:"space-y-3 mb-6",children:[s.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[s.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),"自由讨论模式"]}),s.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[s.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),"主持人模式"]}),s.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[s.jsx("div",{className:"w-2 h-2 bg-orange-500 rounded-full"}),"智能共识判断"]})]}),s.jsx("button",{onClick:()=>e("setup"),disabled:t.agents.length<2,className:"w-full bg-purple-600 text-white py-3 rounded-xl hover:bg-purple-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors font-medium",children:t.agents.length<2?"需要至少2个智能体":"创建新讨论"})]})}),s.jsx("div",{className:"group",children:s.jsxs("div",{className:"bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all p-8 border border-gray-100 group-hover:border-green-200 fixed-size-card",children:[s.jsxs("div",{className:"flex items-center gap-4 mb-6",children:[s.jsx("div",{className:"w-16 h-16 bg-green-100 rounded-xl flex items-center justify-center group-hover:bg-green-200 transition-colors",children:s.jsx(Be,{size:32,className:"text-green-600"})}),s.jsxs("div",{children:[s.jsx("h3",{className:"text-xl font-bold text-gray-900",children:"讨论状态"}),s.jsx("p",{className:"text-gray-500",children:"实时监控"})]})]}),t.isDiscussionActive?s.jsxs(s.Fragment,{children:[s.jsx("p",{className:"text-gray-600 mb-6 leading-relaxed",children:"当前有一场讨论正在进行中，您可以实时观看智能体之间的对话， 监控共识度变化。"}),s.jsxs("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4 mb-6",children:[s.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[s.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full animate-pulse"}),s.jsx("span",{className:"font-medium text-green-800",children:"讨论进行中"})]}),s.jsxs("p",{className:"text-green-700 text-sm",children:["话题：",(n=t.currentDiscussion)==null?void 0:n.topic]})]}),s.jsx("button",{onClick:()=>e("discussion"),className:"w-full bg-green-600 text-white py-3 rounded-xl hover:bg-green-700 transition-colors font-medium",children:"进入讨论室"})]}):s.jsxs(s.Fragment,{children:[s.jsx("p",{className:"text-gray-600 mb-6 leading-relaxed",children:"目前没有进行中的讨论。创建智能体并配置讨论参数后， 您就可以开始一场精彩的AI对话。"}),s.jsxs("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6",children:[s.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[s.jsx("div",{className:"w-2 h-2 bg-gray-400 rounded-full"}),s.jsx("span",{className:"font-medium text-gray-600",children:"空闲状态"})]}),s.jsxs("p",{className:"text-gray-500 text-sm",children:["历史讨论：",t.allDiscussions.length," 场"]})]}),s.jsx("button",{onClick:()=>e(t.agents.length<2?"agents":"setup"),className:"w-full bg-gray-400 text-white py-3 rounded-xl hover:bg-gray-500 transition-colors font-medium",children:t.agents.length<2?"先创建智能体":"开始新讨论"})]})]})}),s.jsx("div",{className:"group",children:s.jsxs("div",{className:"bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all p-8 border border-gray-100 group-hover:border-purple-200 fixed-size-card",children:[s.jsxs("div",{className:"flex items-center gap-4 mb-6",children:[s.jsx("div",{className:"w-16 h-16 bg-purple-100 rounded-xl flex items-center justify-center group-hover:bg-purple-200 transition-colors",children:s.jsx(As,{size:32,className:"text-purple-600"})}),s.jsxs("div",{children:[s.jsx("h3",{className:"text-xl font-bold text-gray-900",children:"讨论历史"}),s.jsx("p",{className:"text-gray-500",children:"查看历史记录"})]})]}),s.jsx("p",{className:"text-gray-600 mb-6 leading-relaxed",children:"查看和分析历史讨论记录，了解智能体的对话模式和共识形成过程。"}),s.jsxs("div",{className:"mb-6",children:[s.jsxs("div",{className:"flex items-center justify-between text-sm text-gray-500 mb-2",children:[s.jsx("span",{children:"历史讨论数量"}),s.jsx("span",{className:"font-bold text-purple-600 text-lg",children:t.allDiscussions.length})]}),s.jsx("div",{className:"w-full h-2 bg-gray-200 rounded-full",children:s.jsx("div",{className:"h-full bg-purple-500 rounded-full transition-all",style:{width:`${Math.min(t.allDiscussions.length/10*100,100)}%`}})})]}),s.jsx("button",{onClick:()=>e("history"),className:"w-full bg-purple-600 text-white py-3 rounded-xl hover:bg-purple-700 transition-colors font-medium",children:"查看历史"})]})})]})]}),s.jsxs("div",{className:"bg-white rounded-2xl shadow-xl p-12 border border-gray-100",children:[s.jsx("h2",{className:"text-3xl font-bold text-gray-900 text-center mb-8",children:"系统特色功能"}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:[s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"w-16 h-16 bg-blue-100 rounded-xl flex items-center justify-center mx-auto mb-4",children:s.jsx(Et,{size:32,className:"text-blue-600"})}),s.jsx("h3",{className:"font-semibold text-gray-900 mb-2",children:"智能化对话"}),s.jsx("p",{className:"text-gray-600 text-sm",children:"基于专业领域和性格特征生成真实的对话内容"})]}),s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"w-16 h-16 bg-green-100 rounded-xl flex items-center justify-center mx-auto mb-4",children:s.jsx(za,{size:32,className:"text-green-600"})}),s.jsx("h3",{className:"font-semibold text-gray-900 mb-2",children:"实时共识监控"}),s.jsx("p",{className:"text-gray-600 text-sm",children:"动态计算讨论共识度，智能判断达成一致的时机"})]}),s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"w-16 h-16 bg-purple-100 rounded-xl flex items-center justify-center mx-auto mb-4",children:s.jsx(st,{size:32,className:"text-purple-600"})}),s.jsx("h3",{className:"font-semibold text-gray-900 mb-2",children:"多模式讨论"}),s.jsx("p",{className:"text-gray-600 text-sm",children:"支持自由讨论和主持人模式，适应不同讨论需求"})]}),s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"w-16 h-16 bg-orange-100 rounded-xl flex items-center justify-center mx-auto mb-4",children:s.jsx(Be,{size:32,className:"text-orange-600"})}),s.jsx("h3",{className:"font-semibold text-gray-900 mb-2",children:"丰富的交互"}),s.jsx("p",{className:"text-gray-600 text-sm",children:"支持多种消息类型，包括陈述、提问、同意、反对"})]})]})]})]})})}function Wp(){return s.jsx(Vp,{children:s.jsx(Gf,{children:s.jsx(Hp,{})})})}bl.createRoot(document.getElementById("root")).render(s.jsx(Ya.StrictMode,{children:s.jsx(Wp,{})}));
