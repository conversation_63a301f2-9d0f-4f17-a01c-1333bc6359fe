#!/usr/bin/env python3
"""
数据库迁移脚本：添加 final_moderator_summary 字段到 discussions 表
"""

import sqlite3
import os
import sys

def migrate_database():
    """添加 final_moderator_summary 字段到 discussions 表"""
    
    # 数据库文件路径
    db_path = os.path.join(os.path.dirname(__file__), 'instance', 'multi_agent_system.db')
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return False
    
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查字段是否已存在
        cursor.execute("PRAGMA table_info(discussions)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'final_moderator_summary' in columns:
            print("字段 final_moderator_summary 已存在，无需迁移")
            conn.close()
            return True
        
        # 添加新字段
        print("正在添加 final_moderator_summary 字段...")
        cursor.execute("""
            ALTER TABLE discussions 
            ADD COLUMN final_moderator_summary TEXT
        """)
        
        # 提交更改
        conn.commit()
        print("迁移完成！")
        
        # 验证字段已添加
        cursor.execute("PRAGMA table_info(discussions)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'final_moderator_summary' in columns:
            print("验证成功：final_moderator_summary 字段已添加")
            conn.close()
            return True
        else:
            print("验证失败：字段未正确添加")
            conn.close()
            return False
            
    except Exception as e:
        print(f"迁移失败: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

if __name__ == "__main__":
    print("开始数据库迁移...")
    success = migrate_database()
    
    if success:
        print("数据库迁移成功完成！")
        sys.exit(0)
    else:
        print("数据库迁移失败！")
        sys.exit(1)
